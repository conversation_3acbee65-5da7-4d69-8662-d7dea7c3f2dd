<?php
declare (strict_types = 1);
namespace app\engineering\validate;

use think\Validate;

/**
 * BOM表单验证器
 * Class BomValidate
 * @package app\engineering\validate
 */
class BomValidate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'product_id' => 'require',
        'product_name' => 'require',
        'bom_code' => 'checkBomCode',
        'version' => 'require|checkVersion',
        'remarks' => 'max:500'
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'product_id.require' => '产品ID不能为空',
        'product_name.require' => '产品名称不能为空',
        'bom_code.checkBomCode' => 'BOM编号格式不正确，只能包含字母、数字和下划线',
        'version.require' => '版本号不能为空',
        'version.checkVersion' => '版本号格式不正确，只能包含字母、数字、下划线、破折号和点号',
        'remarks.max' => '备注最多不能超过500个字符'
    ];

    /**
     * 定义验证场景
     * 格式：'场景名' => ['字段名1','字段名2',...]
     *
     * @var array
     */
    protected $scene = [
        'add' => ['product_id', 'product_name', 'bom_code', 'version', 'remarks'],
        'edit' => ['id', 'product_id', 'product_name', 'bom_code', 'version', 'remarks']
    ];

    /**
     * 检查产品名称
     * 如果product_name为空但product_id有值，则在控制器中会自动获取产品名称
     */
    protected function checkProductName($value, $data)
    {
        // 如果产品名称不为空，则直接通过验证
        if (!empty($value)) {
            return true;
        }
        
        // 如果产品ID存在，也通过验证，因为控制器会自动获取产品名称
        if (!empty($data['product_id'])) {
            return true;
        }
        
        // 两者都为空，则验证失败
        return false;
    }

    /**
     * 检查BOM编号
     * 如果为空，将在控制器中自动生成，否则检查格式
     */
    public function checkBomCode($value, $rule, $data = [])
    {
        // 如果为空则允许通过（控制器会自动生成）
        if (empty($value)) {
            return true;
        }
        
        // 验证BOM编号只包含字母、数字和下划线
        if (preg_match('/^[a-zA-Z0-9_\-]+$/', $value)) {
            
            return true;
        }
        
        return false;
    }

    /**
     * 自定义版本号验证规则
     * 允许字母、数字、下划线、破折号和点号
     */
    public function checkVersion($value, $rule, $data = [])
    {
        // 允许的版本号格式，包括常见的V1.0这种格式
        if (preg_match('/^[a-zA-Z0-9_\-\.]+$/', $value)) {
            return true;
        }
        
        return false;
    }
} 