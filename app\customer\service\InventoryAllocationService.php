<?php
namespace app\customer\service;

use think\facade\Db;
use app\warehouse\model\Inventory as InventoryModel;
use app\warehouse\model\OutboundDetail as OutboundDetailModel;
use app\warehouse\model\Inventoryreserve as Inventoryreserve;

class InventoryAllocationService
{
    /**
     * 类说明
     * 自动分配库位产品出库，无需指定库存
     * 
     * 
     * 分配库存
     * @param int $productId 产品ID
     * @param float $quantity 需求数量
     * @param array $reserveRecords 预占记录
     * @param int $orderItemId 订单项ID (可选)
     * @return array 分配结果
     */
    public function allocateInventory($productId, $quantity, $reserveRecords = [], $orderItemId = 0)
    {
        $result = [
            'success' => true,
            'items' => [],
            'total_allocated' => 0,
            'remaining_qty' => $quantity,
            'message' => '',
            'allocation_source' => [] // 记录分配来源 (预占/普通库存) inventory_id
        ];

        // 记录分配详情
        \think\facade\Log::info("开始为产品ID[$productId]分配库存，需求数量[$quantity]");
        
        // 如果没有传入预占记录但有订单项ID，自动获取订单项的预占记录
        if (empty($reserveRecords) && $orderItemId > 0) {
            $reserveRecords = $this->getOrderItemReserves($orderItemId);
            \think\facade\Log::info("为订单项[$orderItemId]查询到".count($reserveRecords)."条预占记录");
        }

        // 1. 优先从预占记录分配
        if (!empty($reserveRecords)) {
            $reserveAllocation = $this->allocateFromReserves($reserveRecords, $quantity);
            
            $result['items'] = array_merge($result['items'], $reserveAllocation['items']);
            $result['total_allocated'] += $reserveAllocation['total_allocated'];
            $result['remaining_qty'] = $reserveAllocation['remaining_qty'];
            
            // 记录从预占分配的数量
            $result['allocation_source']['reserved'] = $reserveAllocation['total_allocated'];
            
            \think\facade\Log::info("从预占记录分配{$reserveAllocation['total_allocated']}单位，剩余需求{$result['remaining_qty']}");
        }

        // 2. 如果还有未分配的数量，从其他可用库存分配
        if ($result['remaining_qty'] > 0) {
            $additionalAllocation = $this->allocateFromAvailable($productId, $result['remaining_qty']);
            $result['items'] = array_merge($result['items'], $additionalAllocation['items']);
            $result['total_allocated'] += $additionalAllocation['total_allocated'];
            $result['remaining_qty'] = $additionalAllocation['remaining_qty'];
            
            // 记录从可用库存分配的数量
            $result['allocation_source']['available'] = $additionalAllocation['total_allocated'];
            
            \think\facade\Log::info("从可用库存分配{$additionalAllocation['total_allocated']}单位，剩余需求{$result['remaining_qty']}");
        }

        // 3. 检查是否完全分配
        if ($result['remaining_qty'] > 0) {
            $result['success'] = false;
            $result['message'] = "库存不足，缺少 {$result['remaining_qty']} 个单位";
            \think\facade\Log::warning("产品ID[$productId]库存分配不足，缺少{$result['remaining_qty']}单位");
        } else {
            \think\facade\Log::info("产品ID[$productId]库存分配成功，总计分配{$result['total_allocated']}单位");
        }

        return $result;
    }

    /**
     * 从预占记录分配
     * @param array $reserves 预占记录
     * @param float $quantity 需求数量
     * @return array 分配结果
     */
    private function allocateFromReserves($reserves, $quantity)
    {
        $result = [
            'items' => [],
            'total_allocated' => 0,
            'remaining_qty' => $quantity
        ];

        foreach ($reserves as $reserve) {
            if ($result['remaining_qty'] <= 0) {
                break;
            }

            // 检查预占记录是否包含必要字段
            if (!isset($reserve['inventory_id']) || empty($reserve['inventory_id'])) {
                \think\facade\Log::warning("预占记录缺少inventory_id字段: " . json_encode($reserve));
                continue; // 跳过无效预占记录
            }

            // 获取库存记录
            $inventory = InventoryModel::find($reserve['inventory_id']);
            if (!$inventory) {
                \think\facade\Log::warning("预占关联的库存记录不存在，预占ID: {$reserve['id']}，库存ID: {$reserve['inventory_id']}");
                continue;
            }
            
            // 确保inventory记录有id
            if (!isset($inventory['id']) || empty($inventory['id'])) {
                \think\facade\Log::warning("库存记录缺少id，预占ID: {$reserve['id']}");
                continue;
            }

            // 计算本次分配数量
            $allocateQty = min($result['remaining_qty'], $reserve['quantity']);
            
            // 预防零或负数分配
            if ($allocateQty <= 0) {
                \think\facade\Log::warning("预占记录[ID:{$reserve['id']}]分配数量为0或负数，跳过");
                continue;
            }

            // 添加分配记录
            $result['items'][] = [
                'inventory_id' => $inventory['id'], // 确保包含inventory_id
                'warehouse_id' => $inventory['warehouse_id'] ?? 0,
                'location_id' => $inventory['location_id'] ?? 0,
                'product_id' => $inventory['product_id'] ?? 0,
                'batch_no' => $inventory['batch_no'] ?? '',
                'quantity' => $allocateQty,
                'is_reserved' => true,
                'reserve_id' => $reserve['id'],
                'allocation_type' => 'reserved' // 标记为预占分配
            ];

            $result['total_allocated'] += $allocateQty;
            $result['remaining_qty'] -= $allocateQty;
            
            \think\facade\Log::info("从预占[ID:{$reserve['id']}]分配{$allocateQty}单位，剩余需求{$result['remaining_qty']}");
        }

        return $result;
    }

    /**
     * 从可用库存分配（FIFO策略）
     * @param int $productId 产品ID
     * @param float $quantity 需求数量
     * @param int $warehouseId 指定仓库ID (可选)
     * @return array 分配结果
     */
    private function allocateFromAvailable($productId, $quantity, $warehouseId = 0)
    {
        $result = [
            'items' => [],
            'total_allocated' => 0,
            'remaining_qty' => $quantity,
            'warehouse_allocation' => [] // 按仓库记录分配情况
        ];

        // 构建查询条件
        $where = [
            ['product_id', '=', $productId],
            ['available_quantity', '>', 0],
            ['status', '=', 1]
        ];
        
        // 如果指定了仓库，则只从该仓库分配
        if ($warehouseId > 0) {
            $where[] = ['warehouse_id', '=', $warehouseId];
        }

        // 查询库存记录 - 按批次时间升序（先进先出FIFO策略）
        $inventories = InventoryModel::where($where)
            ->order('create_time asc')
            ->select()
            ->toArray();
        
        // 记录查询到的库存情况
        $totalAvailable = array_sum(array_column($inventories, 'available_quantity'));
        \think\facade\Log::info("产品[$productId]查询到" . count($inventories) . "条库存记录，总可用数量: $totalAvailable, 需求数量: $quantity");
        
        // 如果没有可用库存，直接返回
        if (empty($inventories)) {
            \think\facade\Log::warning("产品[$productId]没有可用库存");
            return $result;
        }
        
        // 优先级1：首先尝试从单个库位满足全部需求（减少拆分出库的复杂性）
        foreach ($inventories as $inventory) {
            // 检查记录是否包含必要字段
            if (!isset($inventory['id']) || empty($inventory['id'])) {
                \think\facade\Log::warning("库存记录缺少id: " . json_encode($inventory));
                continue; // 跳过无效记录
            }
            
            // 如果该库位能够完全满足需求，直接使用该库位
            if ($inventory['available_quantity'] >= $result['remaining_qty']) {
                $allocateQty = $result['remaining_qty'];
                
                // 添加分配记录
                $result['items'][] = [
                    'inventory_id' => $inventory['id'], // 确保包含inventory_id
                    'warehouse_id' => $inventory['warehouse_id'] ?? 0,
                    'location_id' => $inventory['location_id'] ?? 0,
                    'product_id' => $inventory['product_id'] ?? $productId,
                    'batch_no' => $inventory['batch_no'] ?? '',
                    'quantity' => $allocateQty,
                    'is_reserved' => false,
                    'reserve_id' => 0,
                    'allocation_type' => 'full' // 标记为完全满足
                ];
                
                // 记录仓库分配情况
                $warehouseKey = $inventory['warehouse_id'] ?? 0;
                if (!isset($result['warehouse_allocation'][$warehouseKey])) {
                    $result['warehouse_allocation'][$warehouseKey] = 0;
                }
                $result['warehouse_allocation'][$warehouseKey] += $allocateQty;
                
                $result['total_allocated'] += $allocateQty;
                $result['remaining_qty'] = 0;
                
                \think\facade\Log::info("库位ID[{$inventory['id']}]完全满足需求，分配{$allocateQty}单位");
                
                return $result; // 已满足全部需求，提前返回结果
            }
        }
        
        // 优先级2：如果没有单个库位能满足，则从多个库位分配
        foreach ($inventories as $inventory) {
            if ($result['remaining_qty'] <= 0) {
                break; // 已分配完毕
            }
            
            // 检查记录是否包含必要字段
            if (!isset($inventory['id']) || empty($inventory['id'])) {
                \think\facade\Log::warning("库存记录缺少id: " . json_encode($inventory));
                continue; // 跳过无效记录
            }
            
            // 计算本次分配数量
            $allocateQty = min($result['remaining_qty'], $inventory['available_quantity']);
            
            if ($allocateQty <= 0) {
                continue; // 跳过不可用的库存
            }
            
            // 添加分配记录
            $result['items'][] = [
                'inventory_id' => $inventory['id'], // 确保包含inventory_id
                'warehouse_id' => $inventory['warehouse_id'] ?? 0,
                'location_id' => $inventory['location_id'] ?? 0,
                'product_id' => $inventory['product_id'] ?? $productId,
                'batch_no' => $inventory['batch_no'] ?? '',
                'quantity' => $allocateQty,
                'is_reserved' => false,
                'reserve_id' => 0,
                'allocation_type' => 'partial' // 标记为部分满足
            ];
            
            // 记录仓库分配情况
            $warehouseKey = $inventory['warehouse_id'] ?? 0;
            if (!isset($result['warehouse_allocation'][$warehouseKey])) {
                $result['warehouse_allocation'][$warehouseKey] = 0;
            }
            $result['warehouse_allocation'][$warehouseKey] += $allocateQty;
            
            $result['total_allocated'] += $allocateQty;
            $result['remaining_qty'] -= $allocateQty;
            
            \think\facade\Log::info("库位ID[{$inventory['id']}]分配{$allocateQty}单位，剩余需求{$result['remaining_qty']}");
        }
        
        // 记录最终分配结果
        if ($result['remaining_qty'] > 0) {
            \think\facade\Log::warning("产品[$productId]库存不足，缺少{$result['remaining_qty']}单位");
        } else {
            $warehouseCount = count($result['warehouse_allocation']);
            $locationCount = count($result['items']);
            \think\facade\Log::info("产品[$productId]成功从{$warehouseCount}个仓库的{$locationCount}个库位分配{$result['total_allocated']}单位");
        }
        
        return $result;
    }

    /**
     * 创建出库单明细
     * @param int $outboundId 出库单ID
     * @param array $allocation 分配信息
     * @param array $productInfo 产品信息
     * @param array $options 额外选项
     * @return bool|int 成功返回出库单明细ID，失败返回false
     */
    public function createOutboundDetail($outboundId, $allocation, $productInfo, $options = [])
    {
        try {
            // 检查必要参数
            $requiredFields = ['inventory_id', 'warehouse_id', 'location_id', 'product_id', 'quantity'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (!isset($allocation[$field]) || (is_string($allocation[$field]) && trim($allocation[$field]) === '')) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                $errorMsg = "创建出库单明细失败：缺少必要参数 " . implode(', ', $missingFields);
                \think\facade\Log::error($errorMsg . ", 数据：" . json_encode($allocation));
                throw new \Exception($errorMsg);
            }
            
            // 检查出库单ID
            if (empty($outboundId) || $outboundId <= 0) {
                throw new \Exception("创建出库单明细失败：出库单ID无效");
            }
            
            // 检查产品信息
            if (empty($productInfo) || !isset($productInfo['title'])) {
                \think\facade\Log::warning("创建出库单明细：产品信息不完整，将使用默认值");
            }
            
            $detail = new OutboundDetailModel();
            $detail->outbound_id = $outboundId;
            $detail->warehouse_id = $allocation['warehouse_id'] ?? 0;
            $detail->location_id = $allocation['location_id'] ?? 0;
            $detail->product_id = $allocation['product_id'] ?? ($productInfo['id'] ?? 0);
            $detail->product_code = $productInfo['material_code'] ?? '';
            $detail->product_name = $productInfo['title'] ?? '未知产品';
            $detail->spec = $productInfo['specs'] ?? '';
            $detail->unit = $productInfo['unit'] ?? '';
            $detail->quantity = $allocation['quantity'] ?? 0;
            $detail->batch_no = $allocation['batch_no'] ?? '';
            $detail->price = $productInfo['price'] ?? 0;
            $detail->amount = ($productInfo['price'] ?? 0) * ($allocation['quantity'] ?? 0);
            
            // 添加额外参数
            if (isset($options['ref_order_id'])) {
                $detail->ref_order_id = $options['ref_order_id'];
            }
            
            if (isset($options['ref_order_item_id'])) {
                $detail->ref_order_item_id = $options['ref_order_item_id'];
            }
            
            if (isset($options['ref_delivery_id'])) {
                $detail->ref_delivery_id = $options['ref_delivery_id'];
            }
            
            if (isset($options['ref_delivery_item_id'])) {
                $detail->ref_delivery_item_id = $options['ref_delivery_item_id'];
            }
            
            // 确保inventory_id一定存在且有效
            if (empty($allocation['inventory_id'])) {
                throw new \Exception("创建出库单明细失败：inventory_id 为空");
            }
            
            $detail->inventory_id = $allocation['inventory_id']; // 这里是必需的
            $detail->create_time = time();
            $detail->update_time = time();
            
            if ($detail->save()) {
                // 记录出库单明细创建成功
                \think\facade\Log::info("成功创建出库单明细：出库单[$outboundId]，产品[{$productInfo['title']}]，数量[{$allocation['quantity']}]，库位[{$allocation['location_id']}]");
                return $detail->id;
            } else {
                \think\facade\Log::error("创建出库单明细失败：保存记录失败");
                return false;
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("创建出库单明细异常：" . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 更新库存和预占状态
     * @param array $allocation 分配信息
     * @param int $operatorId 操作人ID
     * @param array $options 额外选项
     * @return bool 成功返回true
     */
    public function updateInventoryAndReserves($allocation, $operatorId, $options = [])
    {
        Db::startTrans();
        try {
            // 检查必要参数
            $requiredFields = ['inventory_id', 'quantity'];
            $missingFields = [];
            
            foreach ($requiredFields as $field) {
                if (!isset($allocation[$field]) || (is_string($allocation[$field]) && trim($allocation[$field]) === '')) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                $errorMsg = "更新库存失败：缺少必要参数 " . implode(', ', $missingFields);
                \think\facade\Log::error($errorMsg . ", 数据：" . json_encode($allocation));
                throw new \Exception($errorMsg);
            }
            
            // 验证操作人ID
            if (empty($operatorId)) {
                \think\facade\Log::warning("更新库存时操作人ID为空，使用默认值0");
                $operatorId = 0;
            }
            
            // 1. 获取库存记录
            $inventory = InventoryModel::find($allocation['inventory_id']);
            if (!$inventory) {
                throw new \Exception('库存记录不存在，库存ID：' . $allocation['inventory_id']);
            }

            // 记录更新前的库存信息
            \think\facade\Log::info("更新库存前状态：ID[{$inventory->id}]，产品[{$inventory->product_id}]，总量[{$inventory->quantity}]，可用[{$inventory->available_quantity}]，已分配[{$inventory->allocated_quantity}]，预占[{$inventory->reserved_quantity}]");

            // 2. 检查库存是否充足
            $requiredQty = $allocation['quantity'] ?? 0;
            if ($requiredQty <= 0) {
                \think\facade\Log::warning("分配数量为0或负数，跳过库存更新");
                Db::commit();
                return true;
            }
            
            if ($inventory->available_quantity < $requiredQty) {
                throw new \Exception('可用库存不足，当前可用：' . $inventory->available_quantity . '，需要：' . $requiredQty);
            }

            $beforeQty = $inventory->quantity;
            $newQuantity = $inventory->quantity - $requiredQty;
            $newAvailableQuantity = $inventory->available_quantity - $requiredQty;
            $newAllocatedQuantity = $inventory->allocated_quantity + $requiredQty;

            // 确保不会出现负数
            if ($newQuantity < 0 || $newAvailableQuantity < 0) {
                throw new \Exception('库存不能为负数，计算后总量：' . $newQuantity . '，可用：' . $newAvailableQuantity);
            }

            // 3. 更新库存数量
            $inventory->quantity = $newQuantity;
            $inventory->available_quantity = $newAvailableQuantity;
            $inventory->allocated_quantity = $newAllocatedQuantity;

            // 4. 如果是从预占库存分配，需要减少预占数量
            $isReserved = $allocation['is_reserved'] ?? false;
            if ($isReserved) {
                if ($inventory->reserved_quantity < $requiredQty) {
                    throw new \Exception('预占库存不足，当前预占：' . $inventory->reserved_quantity . '，需要：' . $requiredQty);
                }
                $inventory->reserved_quantity -= $requiredQty;
                
                \think\facade\Log::info("从预占库存分配：库存ID[{$inventory->id}]，减少预占数量[{$requiredQty}]");
            }

            if (!$inventory->save()) {
                throw new \Exception('更新库存失败');
            }

            // 5. 更新预占记录状态
            $reserveId = $allocation['reserve_id'] ?? 0;
            if ($isReserved && $reserveId) {
                $reserve = Db::name('inventory_reserve')->where('id', $reserveId)->find();
                if ($reserve) {
                    if ($requiredQty >= $reserve['quantity']) {
                        // 完全使用预占
                        Db::name('inventory_reserve')
                            ->where('id', $reserveId)
                            ->update([
                                'status' => 2, // 已使用
                                'update_time' => time()
                            ]);
                        \think\facade\Log::info("预占记录已完全使用：预占ID[{$reserveId}]");
                    } else {
                        // 部分使用预占
                        Db::name('inventory_reserve')
                            ->where('id', $reserveId)
                            ->update([
                                'quantity' => Db::raw('quantity - ' . $requiredQty),
                                'update_time' => time()
                            ]);
                        \think\facade\Log::info("预占记录部分使用：预占ID[{$reserveId}]，减少数量[{$requiredQty}]");
                    }
                } else {
                    \think\facade\Log::warning("预占记录不存在：预占ID[{$reserveId}]");
                }
            }

            // 6. 记录库存日志
            $logData = [
                'inventory_id' => $inventory->id,
                'warehouse_id' => $inventory->warehouse_id,
                'location_id' => $inventory->location_id,
                'product_id' => $inventory->product_id,
                'before_quantity' => $beforeQty,
                'quantity' => -$requiredQty,
                'after_quantity' => $newQuantity,
                'type' => 2, // 2表示出库
                'ref_type' => $options['ref_type'] ?? 'outbound',
                'ref_id' => $options['ref_id'] ?? ($allocation['outbound_id'] ?? 0),
                'operation_by' => $operatorId,
                'notes' => $options['remark'] ?? '销售出库',
                'create_time' => time()
            ];
            
            Db::name('inventory_log')->insert($logData);

            // 记录更新后的库存状态
            \think\facade\Log::info("更新库存后状态：ID[{$inventory->id}]，总量[{$inventory->quantity}]，可用[{$inventory->available_quantity}]，已分配[{$inventory->allocated_quantity}]，预占[{$inventory->reserved_quantity}]");

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            \think\facade\Log::error("更新库存失败：" . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 处理收货预占
     * @param int $inventoryId 库存ID
     * @param float $quantity 预占数量
     * @param int $operatorId 操作人ID
     * @param array $reserveInfo 预占信息
     * @return bool
     */
    public function handleReceiptReservation($inventoryId, $quantity, $operatorId, $reserveInfo = [])
    {

         
        Db::startTrans();
        try {
            // 获取库存记录
            $inventory = InventoryModel::find($inventoryId);
            if (!$inventory) {
                throw new \Exception('库存记录不存在');
            }

            // 更新库存预占量
            $inventory->reserved_quantity += $quantity;
            $inventory->available_quantity -= $quantity;
            $inventory->save();

            // 创建预占记录
            $reserveData = [
                'inventory_id' => $inventoryId,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'batch_no' => $inventory->batch_no,
                'location_id' => $inventory->location_id,
                'quantity' => $quantity,
                'ref_type' => $reserveInfo['ref_type'] ?? '',
                'ref_id' => $reserveInfo['ref_id'] ?? 0,
                'ref_no' => $reserveInfo['ref_no'] ?? '',
                'receipt_id' => $reserveInfo['receipt_id'] ?? 0,
                'status' => 1, // 预占状态：1=有效
                'create_time' => time(),
                'created_by' => $operatorId,
                'reserve_time' => time(),
                'expire_time' => time() + 86400, // 24小时后过期
                'update_time' => time()
            ];

            // 添加调试日志
            trace('创建预占记录 - reserveData: ' . json_encode($reserveData), 'debug');

            // 插入预占记录
            Db::name('inventory_reserve')->insert($reserveData);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            trace('预占处理失败: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * 同步库存数量
     * @param int $inventoryId 库存ID
     * @return bool
     * @throws \Exception
     */
    public function syncInventoryQuantities($inventoryId)
    {
        Db::startTrans();
        try {
            // 获取并锁定库存记录
            $inventory = InventoryModel::where('id', $inventoryId)->lock(true)->find();
            if (!$inventory) {
                throw new \Exception('库存记录不存在');
            }

            // 获取实际预占总量
            $reservedTotal = Db::name('inventory_reserve')
                ->where([
                    ['inventory_id', '=', $inventoryId],
                    ['status', '=', 1] // 有效预占
                ])
                ->sum('quantity');

            // 更新库存记录
            $inventory->reserved_quantity = $reservedTotal;
            $inventory->available_quantity = $inventory->quantity - $inventory->allocated_quantity - $reservedTotal;

            if ($inventory->available_quantity < 0) {
                throw new \Exception('同步后的可用数量不能为负数');
            }

            if (!$inventory->save()) {
                throw new \Exception('更新库存失败');
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 修复历史预占数据
     * @return array 修复结果
     */
    public function fixHistoricalReservations()
    {
        $result = [
            'success' => true,
            'fixed_count' => 0,
            'errors' => []
        ];

        // 获取所有有效的预占记录
        $reserves = Db::name('inventory_reserve')
            ->where('status', 1)
            ->select();

        // 按库存ID分组预占记录
        $inventoryReserves = [];
        foreach ($reserves as $reserve) {
            $inventoryId = $reserve['inventory_id'];
            if (!isset($inventoryReserves[$inventoryId])) {
                $inventoryReserves[$inventoryId] = 0;
            }
            $inventoryReserves[$inventoryId] += $reserve['quantity'];
        }

        // 更新每个库存记录
        foreach ($inventoryReserves as $inventoryId => $totalReserved) {
            try {
                $this->syncInventoryQuantities($inventoryId);
                $result['fixed_count']++;
            } catch (\Exception $e) {
                $result['errors'][] = "库存ID {$inventoryId}: " . $e->getMessage();
            }
        }

        $result['success'] = empty($result['errors']);
        return $result;
    }

    /**
     * 获取订单项的预占记录
     * @param int $orderItemId 订单项ID
     * @return array 预占记录列表
     */
    public function getOrderItemReserves($orderItemId)
    {
        // 查询与订单项相关的所有预占记录
        $reserves = Db::name('inventory_reserve')
            ->where([
                ['ref_type', '=', 'customer_order_detail'],
                ['ref_id', '=', $orderItemId],
                ['status', '=', 1] // 状态为有效预占
            ])
            ->select()
            ->toArray();
            
        return $reserves;
    }

    /**
     * 记录库存分配详情
     * @param array $allocation 分配结果
     * @param array $context 上下文信息
     * @return void
     */
    public function logAllocationDetails($allocation, $context = [])
    {
        // 提取上下文信息
        $orderId = $context['order_id'] ?? 0;
        $orderNo = $context['order_no'] ?? '';
        $deliveryId = $context['delivery_id'] ?? 0;
        $deliveryNo = $context['delivery_no'] ?? '';
        $productId = $context['product_id'] ?? 0;
        $productName = $context['product_name'] ?? '未知产品';
        
        // 初始化日志消息
        $message = "库存分配详情：\n";
        $message .= "订单：{$orderNo}(ID:{$orderId})，发货单：{$deliveryNo}(ID:{$deliveryId})，产品：{$productName}(ID:{$productId})\n";
        
        // 分配总览
        $message .= "请求数量：{$context['requested_qty']}\n";
        $message .= "分配数量：{$allocation['total_allocated']}\n";
        $message .= "未分配数量：{$allocation['remaining_qty']}\n";
        $message .= "分配状态：" . ($allocation['success'] ? '成功' : '不足') . "\n";
        
        // 分配来源统计
        $reservedQty = $allocation['allocation_source']['reserved'] ?? 0;
        $availableQty = $allocation['allocation_source']['available'] ?? 0;
        $message .= "从预占分配：{$reservedQty}\n";
        $message .= "从可用库存分配：{$availableQty}\n";
        
        // 按仓库统计
        if (!empty($allocation['items'])) {
            $warehouseStats = [];
            foreach ($allocation['items'] as $item) {
                $warehouseId = $item['warehouse_id'];
                if (!isset($warehouseStats[$warehouseId])) {
                    $warehouseStats[$warehouseId] = [
                        'total' => 0,
                        'locations' => []
                    ];
                }
                $warehouseStats[$warehouseId]['total'] += $item['quantity'];
                
                $locationId = $item['location_id'];
                if (!isset($warehouseStats[$warehouseId]['locations'][$locationId])) {
                    $warehouseStats[$warehouseId]['locations'][$locationId] = 0;
                }
                $warehouseStats[$warehouseId]['locations'][$locationId] += $item['quantity'];
            }
            
            $message .= "\n按仓库分配详情：\n";
            foreach ($warehouseStats as $warehouseId => $stat) {
                $warehouseName = $this->getWarehouseName($warehouseId);
                $message .= "  仓库：{$warehouseName}(ID:{$warehouseId})，分配：{$stat['total']}\n";
                
                foreach ($stat['locations'] as $locationId => $qty) {
                    $locationName = $this->getLocationName($locationId);
                    $message .= "    库位：{$locationName}(ID:{$locationId})，分配：{$qty}\n";
                }
            }
        }
        
        // 如果有错误消息
        if (!empty($allocation['message'])) {
            $message .= "\n错误信息：{$allocation['message']}\n";
        }
        
        // 记录到日志
        \think\facade\Log::info($message);
        
        // 如果分配不足，记录警告日志
        if (!$allocation['success']) {
            \think\facade\Log::warning("产品[{$productName}]库存分配不足，缺少{$allocation['remaining_qty']}单位");
        }
    }
    
    /**
     * 获取仓库名称
     * @param int $warehouseId 仓库ID
     * @return string 仓库名称
     */
    private function getWarehouseName($warehouseId)
    {
        if (empty($warehouseId)) {
            return '未指定仓库';
        }
        
        static $warehouseCache = [];
        
        if (!isset($warehouseCache[$warehouseId])) {
            $warehouseCache[$warehouseId] = Db::name('warehouse')
                ->where('id', $warehouseId)
                ->value('name') ?? '未知仓库';
        }
        
        return $warehouseCache[$warehouseId];
    }
    
    /**
     * 获取库位名称
     * @param int $locationId 库位ID
     * @return string 库位名称
     */
    private function getLocationName($locationId)
    {
        if (empty($locationId)) {
            return '默认库位';
        }
        
        static $locationCache = [];
        
        if (!isset($locationCache[$locationId])) {
            $locationCache[$locationId] = Db::name('warehouse_location')
                ->where('id', $locationId)
                ->value('name') ?? '未知库位';
        }
        
        return $locationCache[$locationId];
    }

    /**
     * 验证分配结果是否有效
     * @param array $allocation 分配结果
     * @param float $requestedQty 请求数量
     * @return array 验证结果 ['valid' => bool, 'message' => string]
     */
    public function validateAllocation($allocation, $requestedQty)
    {
        $result = [
            'valid' => true,
            'message' => '分配有效'
        ];
        
        // 1. 检查分配总数是否匹配请求数量
        if ($allocation['total_allocated'] < $requestedQty) {
            $result['valid'] = false;
            $result['message'] = "分配数量不足，需要：{$requestedQty}，实际分配：{$allocation['total_allocated']}";
            return $result;
        }
        
        // 2. 检查分配项是否有效
        if (empty($allocation['items'])) {
            $result['valid'] = false;
            $result['message'] = "没有分配项";
            return $result;
        }
        
        // 3. 检查每个分配项的必要字段和库存是否仍然有效
        $invalidItems = [];
        $requiredFields = ['inventory_id', 'warehouse_id', 'location_id', 'product_id', 'quantity'];
        
        foreach ($allocation['items'] as $index => $item) {
            // 检查必要字段
            $missingFields = [];
            foreach ($requiredFields as $field) {
                if (!isset($item[$field]) || (is_string($item[$field]) && trim($item[$field]) === '')) {
                    $missingFields[] = $field;
                }
            }
            
            if (!empty($missingFields)) {
                $invalidItems[] = "分配项[$index]缺少必要字段: " . implode(', ', $missingFields);
                continue;
            }
            
            // 获取当前库存
            $inventory = InventoryModel::find($item['inventory_id']);
            if (!$inventory) {
                $invalidItems[] = "库存ID[{$item['inventory_id']}]不存在";
                continue;
            }
            
            // 验证可用库存是否足够
            if ($inventory->available_quantity < $item['quantity']) {
                $invalidItems[] = "库存ID[{$item['inventory_id']}]可用数量不足，需要：{$item['quantity']}，可用：{$inventory->available_quantity}";
            }
        }
        
        if (!empty($invalidItems)) {
            $result['valid'] = false;
            $result['message'] = "部分分配项无效:\n" . implode("\n", $invalidItems);
        }
        
        return $result;
    }
    
    /**
     * 创建并分配库存到出库单
     * @param int $outboundId 出库单ID
     * @param int $productId 产品ID
     * @param float $quantity 数量
     * @param array $productInfo 产品信息
     * @param array $options 额外选项
     * @return array 结果
     */
    public function allocateToOutbound($outboundId, $productId, $quantity, $productInfo, $options = [])
    {
        $result = [
            'success' => false,
            'message' => '',
            'outbound_details' => []
        ];
        
        try {
            // 参数验证
            if (empty($outboundId) || $outboundId <= 0) {
                throw new \Exception('出库单ID无效');
            }
            
            if (empty($productId) || $productId <= 0) {
                throw new \Exception('产品ID无效');
            }
            
            if ($quantity <= 0) {
                throw new \Exception('分配数量必须大于0');
            }
            
            if (empty($productInfo) || !is_array($productInfo)) {
                throw new \Exception('产品信息无效');
            }
            
            // 1. 分配库存
            $orderItemId = $options['order_item_id'] ?? 0;
            $allocation = $this->allocateInventory($productId, $quantity, [], $orderItemId);
            
            // 记录分配详情
            $context = [
                'order_id' => $options['order_id'] ?? 0,
                'order_no' => $options['order_no'] ?? '',
                'delivery_id' => $options['delivery_id'] ?? 0,
                'delivery_no' => $options['delivery_no'] ?? '',
                'product_id' => $productId,
                'product_name' => $productInfo['title'] ?? '未知产品',
                'requested_qty' => $quantity
            ];
            $this->logAllocationDetails($allocation, $context);
            
            // 2. 验证分配结果
            $validation = $this->validateAllocation($allocation, $quantity);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message'],
                    'allocation' => $allocation
                ];
            }
            
            // 3. 创建出库单明细并更新库存
            if ($allocation['success']) {
                Db::startTrans();
                try {
                    $detailIds = [];
                    
                    foreach ($allocation['items'] as $index => $item) {
                        // 验证分配项
                        $requiredFields = ['inventory_id', 'warehouse_id', 'location_id', 'product_id', 'quantity'];
                        $missingFields = [];
                        
                        foreach ($requiredFields as $field) {
                            if (!isset($item[$field])) {
                                $missingFields[] = $field;
                            }
                        }
                        
                        if (!empty($missingFields)) {
                            \think\facade\Log::error("分配项缺少必要字段: " . implode(', ', $missingFields) . ", 数据: " . json_encode($item));
                            throw new \Exception("分配项缺少必要字段: " . implode(', ', $missingFields));
                        }
                        
                        // 创建出库单明细
                        $detailId = $this->createOutboundDetail($outboundId, $item, $productInfo, $options);
                        if (!$detailId) {
                            throw new \Exception('创建出库单明细失败，产品: ' . ($productInfo['title'] ?? '未知产品'));
                        }
                        
                        $detailIds[] = $detailId;
                        
                        // 更新选项，添加出库单ID
                        $updateOptions = array_merge($options, ['ref_id' => $outboundId, 'ref_type' => 'outbound']);
                        
                        // 更新库存
                        $operatorId = $options['operator_id'] ?? 0;
                        $this->updateInventoryAndReserves($item, $operatorId, $updateOptions);
                       
                    }
                    
                     //更新预占.
                     $this->updateReserve($options['yz_ref_type'], $options['yz_ref_id'], $quantity);

                    Db::commit();
                    
                    $result = [
                        'success' => true,
                        'message' => '成功分配并创建出库单明细',
                        'outbound_details' => $detailIds,
                        'allocation' => $allocation
                    ];
                    
                } catch (\Exception $e) {
                    Db::rollback();
                    \think\facade\Log::error("创建出库单明细失败: " . $e->getMessage());
                    throw $e;
                }
            } else {
                $result = [
                    'success' => false,
                    'message' => "库存分配不足: " . $allocation['message'],
                    'allocation' => $allocation
                ];
            }
            
            return $result;
            
        } catch (\Exception $e) {
            \think\facade\Log::error("分配库存到出库单失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    //更新预占数量
    public function updateReserve($ref_type, $ref_id, $quantity)
    {
        $reserve = Inventoryreserve::where('ref_type', $ref_type)
            ->where('ref_id', $ref_id)
            ->find();
        if ($reserve) {
            $reserve->dec('quantity', $quantity);
            $reserve->inc('used_quantity', $quantity);
            $reserve->update_time = time();
            $reserve->save();
        }
    }
} 