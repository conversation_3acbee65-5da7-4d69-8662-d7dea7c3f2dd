<?php
declare (strict_types = 1);
namespace app\engineering\model;

use think\Model;
use think\facade\Db;

class BomCheckRecord extends Model
{
    // 设置表名，不使用默认的模型名转换规则
    protected $name = 'bom_check_record';
    
    // 如果有前缀需要关闭前缀，或者指定正确的表名
    // protected $autoWriteTimestamp = true; // 自动写入时间戳
    
    /**
     * 添加审核记录
     * @param array $data 记录数据
     * @return int
     * @throws \Exception 当添加失败时抛出异常
     */
    public function add($data)
    {
        try {
            // 验证必要字段
            if (empty($data['bom_id'])) {
                throw new \Exception('BOM ID不能为空');
            }
            
            if (empty($data['admin_id'])) {
                throw new \Exception('管理员ID不能为空');
            }
            
            if (!isset($data['status'])) {
                throw new \Exception('审核状态不能为空');
            }
            
            if (empty($data['check_time'])) {
                $data['check_time'] = time();
            } else {
                // 确保时间戳是整数类型
                $data['check_time'] = (int)$data['check_time'];
            }
            
            // 添加创建和更新时间
            $data['create_time'] = time();
            $data['update_time'] = time();
            
            // 直接使用Db查询构建器插入数据，而不是通过模型的insertGetId
            $id = Db::name($this->name)->insertGetId($data);
            
            if (!$id) {
                throw new \Exception('添加审核记录失败');
            }
            
            return $id;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('添加审核记录失败: ' . $e->getMessage(), 'error');
            throw $e; // 重新抛出异常，让调用者处理
        }
    }
    
    /**
     * 获取最近一条审核记录
     * @param int $bom_id BOM ID
     * @return array|null
     */
    public function getLastRecord($bom_id)
    {
        return Db::name($this->name)
                ->where('bom_id', $bom_id)
                ->order('id desc')
                ->find();
    }
    
    /**
     * 获取审核记录列表
     * @param int $bom_id BOM ID
     * @return array
     */
    public function getRecordsByBomId($bom_id)
    {
        $records = Db::name($this->name)
                ->where('bom_id', $bom_id)
                ->order('id desc')
                ->select()
                ->toArray();
                
        // 处理每条记录的数据格式
        foreach ($records as &$item) {
            // 格式化时间戳为日期时间
            if (isset($item['check_time'])) {
                $item['check_time_text'] = date('Y-m-d H:i:s', (int)$item['check_time']);
            }
            
            // 格式化状态
            if (isset($item['status'])) {
                $statusMap = [
                    0 => '提交审核',
                    1 => '审核通过',
                    2 => '审核拒绝',
                    3 => '撤回审核',
                    4 => '反确认(作废)'
                ];
                $item['status_text'] = $statusMap[$item['status']] ?? '未知状态';
            }
        }
        
        return $records;
    }
    
    /**
     * 检查表是否存在
     * @param string $tableName 表名
     * @return bool
     */
    private function tableExists($tableName)
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }
} 