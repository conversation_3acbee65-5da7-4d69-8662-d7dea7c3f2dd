{extend name="../../base/view/common/base" /}

{block name="head"}
<style>
    .delivery-error .layui-table-cell {
        background-color: #FFD0D0 !important;
        color: #FF0000 !important;
    }
    .delivery-warning .layui-table-cell {
        background-color: #FFFFD0 !important;
        color: #FF6600 !important;
    }
</style>
{/block}

{block name="body"}
<div class="p-page">
    <form class="layui-form" id="deliveryForm" lay-filter="deliveryForm">
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-block">
                                <input type="text" id="order_no" name="order_no" class="layui-input" readonly />
                                <input type="hidden" id="order_id" name="order_id" value="{$Request.param.order_id|default=''}" />
                            </div>
                        </div>
                    
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户名称</label>
                            <div class="layui-input-block">
                                <input type="text" id="customer_name" name="customer_name" class="layui-input" readonly />
                                <input type="hidden" id="customer_id" name="customer_id" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">收货地址</label>
                            <div class="layui-input-block">
                                <input type="text" id="address" name="address" class="layui-input" required lay-verify="required" placeholder="请输入收货地址" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系人</label>
                            <div class="layui-input-block">
                                <input type="text" id="contact" name="contact" class="layui-input" required lay-verify="required" placeholder="请输入联系人" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系电话</label>
                            <div class="layui-input-block">
                                <input type="text" id="phone" name="phone" class="layui-input" required lay-verify="required|phone" placeholder="请输入联系电话" />
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">物流信息</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">发货方式</label>
                            <div class="layui-input-block">
                                <select name="delivery_type" lay-verify="required">
                                    <option value="">请选择发货方式</option>
                                    <option value="1">公司物流</option>
                                    <option value="2">外部物流</option>
                                    <option value="3">客户自提</option>
                                </select>
                            </div>
                        </div>
                       
                        <div class="layui-form-item">
                            <label class="layui-form-label">发货日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="expect_time" id="expect_time" lay-verify="required" placeholder="请选择预计发货日期" class="layui-input" />
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">特殊要求</label>
                            <div class="layui-input-block">
                                <textarea name="remark" placeholder="请输入特殊要求或备注信息" class="layui-textarea"></textarea>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">发货商品</div>
            <div class="layui-card-body">
                <table class="layui-table" id="itemTable" lay-filter="itemTable">
                    <style>
                        #itemTable {
                            width: 100%;
                            table-layout: fixed;
                        }
                        .delivery-error {
                            background-color: #FFE4E1;
                        }
                        .delivery-warning {
                            background-color: #FFFACD;
                        }
                    </style>
                    <thead>
                        <tr >
                            <th lay-data="{field:'id', width:60}">ID</th>
                            <th lay-data="{field:'product_name', width:200}">商品名称</th>
                            <th lay-data="{field:'material_code', width:150}">产品型号</th>
                            <th lay-data="{field:'unit', width:60}">单位</th>
                            <th lay-data="{field:'quantity', width:90}">订单数量</th>
                            <th lay-data="{field:'delivered_qty', width:90}">已发数量</th>
                            <th lay-data="{field:'inventory_qty', width:90}">库存数量</th>
                             <th lay-data="{field:'delivery_qty', width:130, edit: 'text'}">本次发货</th>
                            <th lay-data="{field:'remark', width:150, edit: 'text'}">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 商品列表将通过Ajax加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveDelivery">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
            </div>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    // 扩展Date对象的format方法
    Date.prototype.format = function(fmt) {
        var o = {
            "M+": this.getMonth() + 1, // 月份
            "d+": this.getDate(), // 日
            "H+": this.getHours(), // 小时
            "m+": this.getMinutes(), // 分
            "s+": this.getSeconds(), // 秒
            "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
            "S": this.getMilliseconds() // 毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };

    const moduleInit = ['tool'];
    
    function gouguInit() {
        var tool = layui.tool, 
        form = layui.form, 
        laydate = layui.laydate, 
        $ = layui.jquery,
        table = layui.table;
        var orderId = $("#order_id").val();
        
        // 生成随机发货单号
        var deliveryNo = 'FH' + new Date().format('yyyyMMddHHmmss') + Math.floor(Math.random() * 9000 + 1000);
        $("input[name='delivery_no']").val(deliveryNo);
        
        // 初始化日期选择器
        laydate.render({
            elem: '#expect_time',
            type: 'date'
        });
        
        // 如果有订单ID，则加载订单信息
        if (orderId) {
            console.log('订单ID:', orderId);
            loadOrderInfo(orderId);
        } else {
            // 没有订单ID，弹出选择订单窗口
            tool.popup({
                title: '选择订单',
                area: ['900px', '600px'],
                btn: false,
                content: '/customer/delivery/selectOrder',
                success: function(layero, index) {
                    // 页面会处理关闭窗口的逻辑
                }
            });
        }
        
        // 加载订单信息
        function loadOrderInfo(orderId) {
            tool.get('/customer/delivery/getOrderInfo', {id: orderId}, function(res) {
                if (res.code == 0) {
                    var order = res.data.order;
                    // 设置订单基础信息
                    $("#order_no").val(order.order_no);
                    $("#customer_id").val(order.customer_id);
                    $("#customer_name").val(order.customer ? order.customer.name : '');
                    
                    // 如果有收货地址信息，则填充
                    if (order.address) {
                        $("#address").val(order.address);
                    }
                    if (order.contact) {
                        $("#contact").val(order.contact);
                    }
                    if (order.phone) {
                        $("#phone").val(order.phone);
                    }
                    
                    // 加载订单商品
                    loadOrderItems(orderId);
                } else {
                    layer.msg(res.msg);
                }
            });
        }
        
        // 加载订单商品
        function loadOrderItems(orderId) {
            tool.get('/customer/delivery/getOrderItems', {order_id: orderId}, function(res) {
                if (res.code == 0) {
                    var items = res.data.items;
                    console.log('订单商品数据:', items); // 添加调试输出
                    var html = '';
                    
                    // 构建商品列表HTML
                    for (var i = 0; i < items.length; i++) {
                        var item = items[i];
                       
                       
 
                // 提取属性并赋予默认值
                var quantity = typeof item.quantity === 'number' ? item.quantity : 0;
                var deliveredQty = typeof item.delivered_qty === 'number' ? item.delivered_qty : 0;

                // 计算剩余数量
                var remainQty = quantity - deliveredQty;
                if (remainQty < 0) {
                    remainQty = 0;
                }
                        
                        console.log('商品项:', item.delivered_qty); // 添加单个商品调试输出
                        
                        // 获取商品库存信息
                        var inventoryQty = item.inventory_qty || 0;
                        var availableQty = inventoryQty;
                        
                        html += '<tr>';
                        html += '<td>' + item.id + '</td>';
                        html += '<td>' + item.product_name + '</td>';
                        html += '<td>' + (item.material_code || '') + '</td>';
                        html += '<td>' + (item.unit || '') + '</td>';
                        html += '<td>' + item.quantity + '</td>';
                        html += '<td>' + (item.delivered_qty || 0) + '</td>';
                        html += '<td>' + inventoryQty + '</td>';
                        html += '<td>' + (remainQty > 0 ? remainQty : 0) + '</td>';
                        html += '<td></td>';
                        html += '</tr>';
                    }
                    
                    // 更新表格并初始化
                    $("#itemTable tbody").html(html);
                    table.init('itemTable', {});
                    
                    // 监听单元格编辑
                    table.on('edit(itemTable)', function(obj) {
                        var value = obj.value;
                        var field = obj.field;
                        
                        // 如果是数量字段，进行验证
                        if (field === 'delivery_qty') {
                            // 验证数量是否为数字
                            if (!/^\d+(\.\d+)?$/.test(value)) {
                                obj.tr.find('td[data-field="' + field + '"] .layui-table-cell').addClass('delivery-error');
                                tool.msg('请输入有效的数量');
                                return;
                            }
                            
                            // 获取相关数据
                            var orderQty = parseFloat(obj.data.quantity);
                            var deliveredQty = parseFloat(obj.data.delivered_qty || 0);
                            var inputQty = parseFloat(value);
                            var remainQty = orderQty - deliveredQty;
                            var inventory_qty = parseFloat(obj.data.inventory_qty || 0);
                           
                            // 验证发货数量是否超过可发货数量
                            if (inputQty > remainQty) {
                                obj.tr.find('td[data-field="' + field + '"] .layui-table-cell').addClass('delivery-error');
                                layer.msg('发货数量不能超过可发货数量');
                                // 重置数量为0
                                obj.update({
                                    delivery_qty: 0
                                });
                                return;
                            }
                            
                            // 验证发货数量是否超过库存数量
                            if (inputQty > inventory_qty) {
                                obj.tr.find('td[data-field="' + field + '"] .layui-table-cell').addClass('delivery-error');
                                layer.msg('库存不足，发货数量不能超过当前库存数量');
                                // 重置数量为0
                                obj.update({
                                    delivery_qty: 0
                                });
                                return;
                            }
                        }
                    });
                    
                } else {
                    tool.msg(res.msg);
                }
            });
        }
        
        // 提交表单
        form.on('submit(saveDelivery)', function(data) {
            // 获取表单数据
            var formData = data.field;
            console.log('表单数据:', formData); // 添加调试输出
            
            // 获取商品数据
            var tableData = table.cache.itemTable || [];
            console.log('表格数据:', tableData); // 添加调试输出
            var items = [];
            
            // 验证库存是否充足
            var hasInsufficientStock = false;
            var insufficientStockItems = [];
            
            for (var i = 0; i < tableData.length; i++) {
                var item = tableData[i];
                // 只检查有发货数量的商品
                if (item.delivery_qty && parseFloat(item.delivery_qty) > 0) {
                    // 检查库存是否充足
                    if (parseFloat(item.delivery_qty) > parseFloat(item.inventory_qty || 0)) {
                        hasInsufficientStock = true;
                        insufficientStockItems.push({
                            name: item.product_name,
                            code: item.material_code,
                            inventory: item.inventory_qty || 0,
                            delivery: item.delivery_qty
                        });
                    }
                    
                    items.push({
                        order_item_id: item.id,
                        delivery_qty: item.delivery_qty,
                        remark: item.remark || ''
                    });
                }
            }
            
            // 如果有库存不足的商品，阻止表单提交
            if (hasInsufficientStock) {
                var errorMsg = '以下商品库存不足，无法提交：<br>';
                for (var j = 0; j < insufficientStockItems.length; j++) {
                    var errorItem = insufficientStockItems[j];
                    errorMsg += '- ' + errorItem.name + ' (' + errorItem.code + '): 库存' + errorItem.inventory + '，发货' + errorItem.delivery + '<br>';
                }
                layer.alert(errorMsg, {icon: 2, title: '库存不足'});
                return false;
            }
            
            console.log('提交的商品数据:', items); // 添加调试输出
            
            // 验证是否有发货商品
            if (items.length === 0) {
                tool.msg('请至少选择一件商品发货');
                return false;
            }
            
            // 提交数据
            tool.post('/customer/delivery/save', {
                delivery: formData,
                items: items
            }, function(res) {
                if (res.code == 0) {
                    layer.msg('提交成功，已添加到待出库清单', {icon: 1});
                    tool.close();

                } else {
                    layer.msg(res.msg);
                }
            });
            
            return false;
        });
        
        // 取消按钮
        $("#btnCancel").click(function() {
            tool.tabChange('/customer/delivery/index');
        });
    }
</script>
{/block} 