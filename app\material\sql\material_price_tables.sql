-- 物料供应商价格表
DROP TABLE IF EXISTS `oa_material_supplier_price`;
CREATE TABLE `oa_material_supplier_price` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '价格ID',
  `material_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物料ID',
  `supplier_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `supplier_code` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商编号',
  `priority` varchar(20) NOT NULL DEFAULT '' COMMENT '采购优先级：首选、备选',
  `min_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '采购下限',
  `max_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '采购上限',
  `tax_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '含税单价',
  `tax_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '税率(%)',
  `no_tax_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '不含税单价',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料供应商价格表';

-- 物料外协价格表
DROP TABLE IF EXISTS `oa_material_outsource_price`;
CREATE TABLE `oa_material_outsource_price` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '价格ID',
  `material_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物料ID',
  `supplier_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '外协商ID（使用供应商表）',
  `outsource_code` varchar(50) NOT NULL DEFAULT '' COMMENT '外协商编号',
  `priority` varchar(20) NOT NULL DEFAULT '' COMMENT '外协优先级：首选、备选',
  `min_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '外协下限',
  `max_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '外协上限',
  `tax_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '含税单价',
  `tax_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '税率(%)',
  `no_tax_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '不含税单价',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料外协价格表';