<?php
namespace app\material\model;

use think\Model;

class Archive extends Model
{
    protected $table = 'material_archive';
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'material_code'         => 'string',
        'material_name'         => 'string',
        'category_id'          => 'int',
        'material_source'      => 'string',
        'material_attachment'  => 'string',
        'model'                => 'string',
        'category'             => 'string',
        'color'                => 'string',
        'material_level'       => 'string',
        'remark'               => 'text',
        'material_shape'       => 'string',
        'attachment'           => 'string',
        'reference_cost'       => 'decimal',
        'sales_price'          => 'decimal',
        'min_sales_price'      => 'decimal',
        'max_sales_price'      => 'decimal',
        'is_price_range_check' => 'tinyint',
        'base_unit'            => 'int',
        'default_warehouse'    => 'int',
        'min_order_qty'        => 'decimal',
        'min_package_qty'      => 'decimal',
        'quality_management'   => 'tinyint',
        'quality_settings'     => 'json',
        'admin_id'             => 'int',
        'create_time'          => 'int',
        'update_time'          => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // JSON字段处理
    protected $json = [];
    
    // 关联物料分类
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    // 关联默认仓库
    public function warehouse()
    {
        return $this->belongsTo(\app\warehouse\model\Warehouse::class, 'default_warehouse', 'id');
    }

    // 关联基本单位
    public function unit()
    {
        return $this->belongsTo(\app\common\model\Unit::class, 'base_unit', 'id');
    }
    
    // 获取器 - 格式化创建时间
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化更新时间
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化质量设置
    public function getQualitySettingsAttr($value)
    {
        if (is_string($value) && !empty($value)) {
            return json_decode($value, true) ?: [];
        }
        return [];
    }
    
    // 修改器 - 质量设置
    public function setQualitySettingsAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value);
        }
        return $value;
    }
    
}