<?php

namespace app\engineering\validate;

use think\Validate;

class ProductValidate extends Validate
{
    protected $rule = [
        'title' => 'require',
        'cate_id' => 'require',
        'id' => 'require',
        'material_code' => 'unique:Product,material_code^delete_time',
        'unit' => 'require',
    ];

    protected $message = [
        'title.require' => '产品名称不能为空',
        'cate_id.require' => '产品分类不能为空',
        'id.require' => '缺少更新条件',
        'material_code.unique' => '物料编号已存在',
        'unit.require' => '产品单位不能为空',
    ];

    protected $scene = [
        'add' => ['title', 'cate_id', 'material_code', 'unit'],
        'edit' => ['id', 'title', 'cate_id', 'unit'],
    ];
} 