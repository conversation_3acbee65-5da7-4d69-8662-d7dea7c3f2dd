{extend name="../../base/view/common/base" /}
{block name="style"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">BOM子项管理</div>
        <div class="layui-card-body">
            <!-- 添加调试信息 -->
            <div style="display:none">DEBUG: BOM ID = {$detail.id}</div>
            
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <h3>BOM信息：{$detail.bom_code} - {$detail.product_name} (版本: {$detail.version})</h3>
                    </div>
                </div>
            </div>
            
            <table class="layui-table" id="itemTable" lay-filter="itemTable"></table>
        </div>
    </div>
</div>

<!-- 源类型格式化 -->
<script type="text/html" id="sourceTypeTpl">
    {{# if(d.source_type == 1){ }}
    <span class="layui-badge layui-bg-blue">自制</span>
    {{# }else if(d.source_type == 2){ }}
    <span class="layui-badge layui-bg-green">外购</span>
    {{# }else if(d.source_type == 3){ }}
    <span class="layui-badge layui-bg-orange">委外</span>
    {{# } }}
</script>

<!-- 操作栏模板 -->
<script type="text/html" id="operationBar">
    {{# var canEdit = ('{$detail.status}' == '0' || '{$detail.status}' == '3'); }}
    {{# if(canEdit){ }}
        {{# if(d.is_needed !== 0){ }}
            <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        {{# } else { }}
            <a class="layui-btn layui-btn-normal layui-btn-xs layui-btn-disabled">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        {{# } }}
    {{# } }}
</script>

{/block}

{block name="js"}
<script>
    const delay_num = 30;
	const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','admin'];
	function gouguInit() {
    layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        
        // 获取BOM ID
        var bomId = '{$detail.id}';
        
        // 渲染表格
        var insTb = table.render({
            elem: '#itemTable',
            url: '{:url("engineering/bom/items")}?bom_id=' + bomId,
            toolbar: function(){
                // 根据BOM状态判断是否可编辑
                var canEdit = ('{$detail.status}' == '0' || '{$detail.status}' == '3');
                var html = '<div class="layui-btn-container">';
                if(canEdit){
                    html += '<button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe654;</i>添加子项</button>';
                    html += '<button class="layui-btn layui-btn-sm" lay-event="import"><i class="layui-icon">&#xe67c;</i>批量导入</button>';
                    html += '<button class="layui-btn layui-btn-sm" lay-event="sortItem"><i class="layui-icon">&#xe658;</i>排序</button>';
                }
                html += '<button class="layui-btn layui-btn-sm" lay-event="export"><i class="layui-icon">&#xe67d;</i>导出</button>';
                html += '</div>';
                return html;
            }(),
            defaultToolbar: ['filter'],
            cellMinWidth: 80,
            cols: [[
                {field: 'sort', title: '排序号', width: 80, sort: true},
                {field: 'material_code', title: '物料编码', width: 160},
                {field: 'material_name', title: '物料名称', minWidth: 160},
                {field: 'product_specs', title: '规格', minWidth: 600},
                {field: 'qty', title: '用量', width: 100, align: 'right'},
                {field: 'uom_name', title: '单位', width: 80},
                {field: 'source_type', title: '来源', width: 100, templet: '#sourceTypeTpl'},
                {field: 'loss_rate', title: '损耗率(%)', width: 100, align: 'right'},
                {field: 'remark', title: '备注', minWidth: 160},
                {field: 'is_needed', title: '是否需要', width: 100, templet: function(d){
                    var canEdit = ('{$detail.status}' == '0' || '{$detail.status}' == '3');
                    if(d.is_needed === 0) {
                        if(canEdit) {
                            return '<a href="javascript:;" class="toggle-needed" data-id="'+d.id+'" data-status="0"><span class="layui-badge layui-bg-gray">不需要</span></a>';
                        } else {
                            return '<span class="layui-badge layui-bg-gray">不需要</span>';
                        }
                    } else {
                        if(canEdit) {
                            return '<a href="javascript:;" class="toggle-needed" data-id="'+d.id+'" data-status="1"><span class="layui-badge layui-bg-green">需要</span></a>';
                        } else {
                            return '<span class="layui-badge layui-bg-green">需要</span>';
                        }
                    }
                }},
                {title: '操作', toolbar: '#operationBar', width: 120, fixed: 'right'}
            ]],
            done: function (res, curr, count) {
                // 全局变量，用于其他可能的功能
                window.canEdit = ('{$detail.status}' == '0' || '{$detail.status}' == '3');
                
                // 为"不需要"的子项添加灰色背景
                var trs = $('.layui-table-body').find('tr');
                layui.each(res.data, function(index, item){
                    if(item.is_needed === 0) {
                        $(trs[index]).css('background-color', '#f2f2f2');
                        $(trs[index]).find('td').css('color', '#999');
                    }
                });
            }
        });
        
        // 工具条点击事件
        table.on('toolbar(itemTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'add') { // 添加
                showAddModel();
            } else if (layEvent === 'import') { // 批量导入
                showImportModel();
            } else if (layEvent === 'sortItem') { // 排序
                showSortModel();
            } else if (layEvent === 'export') { // 导出
                doExport();
            }
        });
        
        // 行工具条点击事件
        table.on('tool(itemTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'edit') { // 修改
                showEditModel(data);
            } else if (layEvent === 'del') { // 删除
                doDel(data.id);
            }
        });
        
        // 显示添加弹窗
        function showAddModel() {
            // 确保bomId有效
            if (!bomId) {
                layer.msg('无法获取BOM ID，请刷新页面后重试', {icon: 2});
                return;
            }
            
            console.log('打开添加子项弹窗，BOM ID: ' + bomId);
            
            layer.open({
                type: 2,
                title: '添加BOM子项',
                content: '{:url("engineering/bom/addItem")}?bom_id=' + bomId,
                area: ['80%', '90%'],
                maxmin: true,
                btn: null, // 不需要默认按钮
                closeBtn: 1, // 显示关闭按钮
                success: function (layero, dIndex) {
                    // 弹窗完成后回调
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    
                    // 监听子页面关闭事件
                    var iframe = layero.find('iframe').get(0);
                    iframe.name = 'add_item_iframe'; // 设置iframe名称
                    iframe.onload = function() {
                        if (iframe.contentWindow.refreshParent) {
                            iframe.contentWindow.refreshParent = function() {
                                // 刷新父页面表格
                                insTb.reload();
                            };
                        }
                    };
                },
                end: function() {
                    // 弹窗关闭时刷新表格
                    insTb.reload();
                }
            });
        }
        
        // 显示编辑弹窗
        function showEditModel(data) {
            layer.open({
                type: 2,
                title: '编辑BOM子项',
                content: '{:url("engineering/bom/editItem")}?id=' + data.id,
                area: ['80%', '90%'],
                maxmin: true,
                btn: null, // 不需要默认按钮
                closeBtn: 1, // 显示关闭按钮
                success: function (layero, dIndex) {
                    // 弹窗完成后回调
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    
                    // 监听子页面关闭事件
                    var iframe = layero.find('iframe').get(0);
                    iframe.name = 'edit_item_iframe'; // 设置iframe名称
                    iframe.onload = function() {
                        if (iframe.contentWindow.refreshParent) {
                            iframe.contentWindow.refreshParent = function() {
                                // 刷新父页面表格
                                insTb.reload();
                            };
                        }
                    };
                },
                end: function() {
                    // 弹窗关闭时刷新表格
                    insTb.reload();
                }
            });
        }
        
        // 显示导入弹窗
        function showImportModel() {
            layer.open({
                type: 2,
                title: '批量导入BOM子项',
                content: '{:url("engineering/bom/importItems")}?bom_id=' + bomId,
                area: ['600px', '400px'],
                success: function (layero, dIndex) {
                    // 弹窗完成后回调
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    
                    // 监听子页面关闭事件
                    var iframe = layero.find('iframe').get(0);
                    iframe.onload = function() {
                        if (iframe.contentWindow.refreshParent) {
                            iframe.contentWindow.refreshParent = function() {
                                // 刷新父页面表格
                                insTb.reload();
                            };
                        }
                    };
                }
            });
        }
        
        // 显示排序弹窗
        function showSortModel() {
            // 不再使用admin.putTempData，直接通过URL传递参数
            layer.open({
                type: 2,
                title: '排序BOM子项',
                content: '{:url("engineering/bom/sortItems")}?bom_id=' + bomId,
                area: ['600px', '600px'],
                success: function (layero, dIndex) {
                    // 弹窗完成后回调
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    
                    // 监听子页面关闭事件
                    var iframe = layero.find('iframe').get(0);
                    iframe.onload = function() {
                        if (iframe.contentWindow.refreshParent) {
                            iframe.contentWindow.refreshParent = function() {
                                // 刷新父页面表格
                                insTb.reload();
                            };
                        }
                    };
                }
            });
        }
        
        // 删除
        function doDel(id) {
            layer.confirm('确定要删除此BOM子项吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (index) {
                layer.close(index);
                layer.load(2);
                $.ajax({
                    url: '{:url("engineering/bom/delItem")}',
                    type: 'delete',
                    data: {
                        id: id
                    },
                    dataType: 'json',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            // 刷新表格
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('删除请求失败', {icon: 2});
                    }
                });
            });
        }
        
        // 导出
        function doExport() {
            window.open('{:url("engineering/bom/exportItems")}?bom_id=' + bomId);
        }
        
        // 监听子页面操作完成事件
        window.refreshItemTable = function() {
            insTb.reload();
        };
        
        // 监听"是否需要"状态切换事件
        $(document).on('click', '.toggle-needed', function(){
            var id = $(this).data('id');
            var currentStatus = $(this).data('status');
            var newStatus = currentStatus === 0 ? 1 : 0;
            var statusText = newStatus === 0 ? '不需要' : '需要';
            
            layer.confirm('确定要将此子项设置为"' + statusText + '"吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (index) {
                layer.close(index);
                layer.load(2);
                $.ajax({
                    url: '{:url("engineering/bom/toggleItemNeeded")}',
                    type: 'post',
                    data: {
                        id: id,
                        status: newStatus
                    },
                    dataType: 'json',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            // 刷新表格
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('请求失败', {icon: 2});
                    }
                });
            });
        });
    });
    }
</script>
{/block} 