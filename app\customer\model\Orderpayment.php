<?php
namespace app\customer\model;

use think\Model;

class Orderpayment extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'customer_order_payment';
    
    // 设置主键
    protected $pk = 'payment_id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

        // 软删除
        use \think\model\concern\SoftDelete;
        protected $deleteTime = 'delete_time';
        protected $defaultSoftDelete = 0;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
   

       // 关联主订单
       public function order()
       {
           return $this->belongsTo(Order::class, 'order_id')
               ->with(['customer']); // 默认预加载客户信息
       }
    
        // 关联创建者用户
    public function adduser()
    {
        return $this->belongsTo('app\user\model\Admin', 'operator_id', 'id')->bind(['create_user_name' => 'name']);
    }   
    
    
} 