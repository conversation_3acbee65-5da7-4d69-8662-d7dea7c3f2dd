{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.form-container {
    padding: 20px;
    background: #fff;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1E9FFF;
}
.material-table {
    margin-top: 20px;
}
.material-table .layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.btn-group {
    margin-top: 20px;
    text-align: center;
}
.required {
    color: #FF5722;
}
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="form-container">
    <form class="layui-form" lay-filter="bomForm" id="bomForm">
        <input type="hidden" name="id" value="{$bom.id|default=0}" />
        
        <!-- 基础资料 -->
        <div class="section-title">基础资料</div>
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> BOM编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_code" value="{$bom.bom_code|default=$bom_code}" 
                               placeholder="使用系统编号" class="layui-input" required lay-verify="required" />
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" 
                                style="position:absolute;right:5px;top:5px;" onclick="generateBomCode()">
                            <i class="layui-icon layui-icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> BOM名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_name" value="{$bom.bom_name|default=''}" 
                               placeholder="请输入" maxlength="100" class="layui-input" required lay-verify="required" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> 产品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_name" value="{$bom.product_name|default=''}" 
                               placeholder="S-18音响SS6" class="layui-input" required lay-verify="required" />
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" 
                                style="position:absolute;right:5px;top:5px;" onclick="selectProduct()">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">产品编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_code" value="{$bom.product_code|default=''}" 
                               placeholder="请输入产品编号" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">客户名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="customer_name" value="{$bom.customer_name|default=''}" 
                               placeholder="请输入客户名称" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">客户编码</label>
                    <div class="layui-input-block">
                        <input type="text" name="customer_code" value="{$bom.customer_code|default=''}" 
                               placeholder="请输入客户编码" class="layui-input" />
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">规格</label>
                    <div class="layui-input-block">
                        <input type="text" name="specifications" value="{$bom.specifications|default=''}" 
                               placeholder="请输入规格" class="layui-input" />
                    </div>
                </div>
            </div>
         
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">产品分类</label>
                    <div class="layui-input-block">
                        <select name="product_category">
                            <option value="">请选择产品分类</option>
                         </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
         
            <div class="layui-col-md8">
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$bom.remark|default=''}</textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 物料列表 -->
        <div class="material-table">
            <div class="section-title">
                物料列表
                <div style="float:right;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="selectMaterial()">
                        选择物料
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="importBom()">
                        导入BOM表
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="clearMaterials()">
                        删除
                    </button>
                </div>
            </div>
            
            <table class="layui-table" id="materialTable">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" lay-filter="allChoose" lay-skin="primary">
                        </th>
                        <th width="80">BOM等级</th>
                        <th width="150">物料编号</th>
                        <th width="200">物料名称</th>
                        <th width="80">图片</th>
                        <th width="100">物料分类</th>
                        <th width="100">规格</th>
                        <th width="80">型号</th>
                        <th width="100">物料来源</th>
                        <th width="100">引用BOM</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody id="materialTableBody">
                    {if isset($materials) && $materials}
                        {volist name="materials" id="material"}
                        <tr data-id="{$material.id}">
                            <td><input type="checkbox" lay-skin="primary"></td>
                            <td>一级</td>
                            <td>{$material.material_code}</td>
                            <td>{$material.material_name}</td>
                            <td><img src="/static/images/default.png" width="40" height="40"></td>
                            <td>{$material.material_category}</td>
                            <td>{$material.specifications}</td>
                            <td>{$material.model}</td>
                            <td>
                                <select name="materials[{$key}][material_source]" class="layui-input">
                                    <option value="自购" {if $material.material_source == '自购'}selected{/if}>自购</option>
                                    <option value="自制" {if $material.material_source == '自制'}selected{/if}>自制</option>
                                    <option value="委外" {if $material.material_source == '委外'}selected{/if}>委外</option>
                                </select>
                            </td>
                            <td>
                                <select name="materials[{$key}][reference_bom]" class="layui-input">
                                    <option value="">请选择</option>
                                    <option value="不引用" {if $material.reference_bom == '不引用'}selected{/if}>不引用</option>
                                </select>
                            </td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeMaterial(this)">删除</button>
                            </td>
                        </tr>
                        {/volist}
                    {else}
                    <tr class="empty-row">
                        <td colspan="11" style="text-align:center;color:#999;">暂无数据</td>
                    </tr>
                    {/if}
                </tbody>
            </table>
        </div>
        
        <!-- 操作按钮 -->
        <div class="btn-group">
            <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="bomSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">取消</button>
        </div>
    </form>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','form'];
function gouguInit() {
    var form = layui.form, tool = layui.tool;
    
    // 生成BOM编号
    window.generateBomCode = function() {
        var date = new Date();
        var dateStr = date.getFullYear() + 
                     (date.getMonth() + 1).toString().padStart(2, '0') + 
                     date.getDate().toString().padStart(2, '0');
        var randomNum = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        var bomCode = 'BOM' + dateStr + randomNum;
        $('input[name="bom_code"]').val(bomCode);
    };
    
    // 选择产品
    window.selectProduct = function() {
        layer.msg('产品选择功能待开发');
    };
    
    // 选择物料
    window.selectMaterial = function() {
        layer.open({
            type: 2,
            title: '选择物料',
            content: '/material/bom/getMaterials',
            area: ['90%', '80%'],
            btn: ['确定', '取消'],
            yes: function(index, layero) {
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                var selectedData = iframeWindow.getSelectedData();
                if (selectedData.length === 0) {
                    layer.msg('请选择物料');
                    return;
                }
                
                // 添加选中的物料到表格
                addMaterialsToTable(selectedData);
                layer.close(index);
            }
        });
    };
    
    // 添加物料到表格
    function addMaterialsToTable(materials) {
        var tbody = $('#materialTableBody');
        var emptyRow = tbody.find('.empty-row');
        if (emptyRow.length > 0) {
            emptyRow.remove();
        }
        
        materials.forEach(function(material, index) {
            var currentIndex = tbody.find('tr').length;
            var row = `
                <tr data-id="${material.id}">
                    <td><input type="checkbox" lay-skin="primary"></td>
                    <td>一级</td>
                    <td>${material.material_code}</td>
                    <td>${material.name}</td>
                    <td><img src="/static/images/default.png" width="40" height="40"></td>
                    <td>${material.category || ''}</td>
                    <td>${material.specifications || ''}</td>
                    <td>${material.model || ''}</td>
                    <td>
                        <select name="materials[${currentIndex}][material_source]" class="layui-input">
                            <option value="自购">自购</option>
                            <option value="自制">自制</option>
                            <option value="委外">委外</option>
                        </select>
                    </td>
                    <td>
                        <select name="materials[${currentIndex}][reference_bom]" class="layui-input">
                            <option value="">请选择</option>
                            <option value="不引用">不引用</option>
                        </select>
                    </td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeMaterial(this)">删除</button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
        
        form.render();
    }
    
    // 删除物料
    window.removeMaterial = function(btn) {
        $(btn).closest('tr').remove();
        
        // 如果没有数据了，显示空行
        var tbody = $('#materialTableBody');
        if (tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="11" style="text-align:center;color:#999;">暂无数据</td></tr>');
        }
    };
    
    // 清空物料
    window.clearMaterials = function() {
        var checkedBoxes = $('#materialTableBody input[type="checkbox"]:checked');
        if (checkedBoxes.length === 0) {
            layer.msg('请选择要删除的物料');
            return;
        }
        
        layer.confirm('确定要删除选中的物料吗？', function(index) {
            checkedBoxes.each(function() {
                $(this).closest('tr').remove();
            });
            
            // 如果没有数据了，显示空行
            var tbody = $('#materialTableBody');
            if (tbody.find('tr').length === 0) {
                tbody.append('<tr class="empty-row"><td colspan="11" style="text-align:center;color:#999;">暂无数据</td></tr>');
            }
            
            layer.close(index);
        });
    };
    
    // 导入BOM表
    window.importBom = function() {
        layer.msg('BOM导入功能待开发');
    };
    
    // 全选
    form.on('checkbox(allChoose)', function(data) {
        var child = $('#materialTableBody input[type="checkbox"]');
        child.each(function(index, item) {
            item.checked = data.elem.checked;
        });
        form.render();
    });
    
    // 表单提交
    form.on('submit(bomSubmit)', function(data) {
        // 收集物料数据
        var materials = [];
        $('#materialTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;
            
            var materialData = {
                material_code: $(this).find('td:eq(2)').text(),
                material_name: $(this).find('td:eq(3)').text(),
                material_category: $(this).find('td:eq(5)').text(),
                specifications: $(this).find('td:eq(6)').text(),
                model: $(this).find('td:eq(7)').text(),
                material_source: $(this).find('select[name*="material_source"]').val(),
                reference_bom: $(this).find('select[name*="reference_bom"]').val(),
                quantity: 1, // 默认数量
                loss_rate: 0 // 默认损耗率
            };
            materials.push(materialData);
        });
        
        // 添加物料数据到表单
        data.field.materials = materials;
        
        var callback = function(res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                setTimeout(function() {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }, 1000);
            }
        };
        
        tool.post("/material/bom/add", data.field, callback);
        return false;
    });
}
</script>
{/block}
<!-- /脚本 -->
