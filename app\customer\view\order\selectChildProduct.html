{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="style"}
<style>
/* BOM弹窗样式 */
.bom-detail-table {width: 100%; border-collapse: collapse;}
.bom-detail-table th, .bom-detail-table td {border: 1px solid #e6e6e6; padding: 8px; text-align: center;}
.bom-detail-table th {background-color: #f2f2f2;}
.bom-toggle {cursor: pointer; color: #1E9FFF;}
.bom-toggle:hover {text-decoration: underline;}
.bom-shortage {color: #F44336; font-weight: bold;}
/* 父商品信息样式 */
.parent-product-info {margin-bottom: 15px; padding: 10px; background-color: #f8f8f8; border-left: 3px solid #1E9FFF;}
.parent-product-info p {margin: 5px 0;}
.parent-product-label {font-weight: bold; display: inline-block; width: 80px;}
</style>
{/block}

{block name="body"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">选择子商品</div>
        <div class="layui-card-body">
            <!-- 父商品信息展示 -->
            <div class="parent-product-info layui-hide" id="parentProductInfo">
                <h3>父商品信息</h3>
                <p><span class="parent-product-label">商品名称:</span><span id="parentProductName"></span></p>
                <p><span class="parent-product-label">商品编码:</span><span id="parentProductCode"></span></p>
                <p><span class="parent-product-label">规格型号:</span><span id="parentProductSpecs"></span></p>
            </div>

            <form class="layui-form" id="searchForm">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商品编码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="code" placeholder="请输入商品编码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">商品名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">商品分类</label>
                        <div class="layui-input-inline">
                            <select name="cate_id" lay-search>
                                <option value="">请选择或搜索分类</option>
                                {foreach name="cates" item="cate"}
                                <option value="{$cate.id}">{$cate.title}</option>
                                {foreach name="cate.children" item="child"}
                                <option value="{$child.id}">&nbsp;&nbsp;├ {$child.title}</option>
                                {foreach name="child.children" item="third"}
                                <option value="{$third.id}">&nbsp;&nbsp;&nbsp;&nbsp;├ {$third.title}</option>
                                {/foreach}
                                {/foreach}
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
            
            <table id="productTable" lay-filter="productTable"></table>
            
            <div class="layui-form-item" style="margin-top: 10px;">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="btnConfirm">确定选择</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var $ = layui.jquery;
        var table = layui.table;
        var form = layui.form;
        var tool = layui.tool;
        
        // 显示父商品信息（如果有）
        if (window.parentProductData) {
            $('#parentProductName').text(window.parentProductData.product_name || '未知');
            $('#parentProductCode').text(window.parentProductData.material_code || '未知');
            $('#parentProductSpecs').text(window.parentProductData.product_specs || '未知');
            $('#parentProductInfo').removeClass('layui-hide');
        }
        
        // 初始化表格
        var tableIns = table.render({
            elem: '#productTable',
            url: '{:url("get_product_datalist")}',
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'material_code', title: '商品编码', width: 120, align: 'center'},
                {field: 'title', title: '商品名称', width: 200, align: 'center'},
                {field: 'specs', title: '规格型号', width: 120, align: 'center'},
                {field: 'cate', title: '商品分类', width: 120, align: 'center'},
                {field: 'unit', title: '单位', width: 80, align: 'center'},
                {field: 'purchase_price', title: '成本价', width: 100, align: 'center'},
                {field: 'has_bom', title: '是否有BOM', width: 100, align: 'center', templet: function(d){
                    if (d.has_bom == 1) {
                        return '<span class="bom-toggle layui-badge layui-bg-green" data-product-id="' + d.id + '">是</span>';
                    } else {
                        return '<span class="layui-badge">否</span>';
                    }
                }},
                {field: 'source_type', title: '自产/外购', width: 100, align: 'center', templet: function(d){
                    return d.source_type == 1 ? '<span class="layui-badge ">自产</span>' : '<span class="layui-badge layui-bg-green">外购</span>';
                }},
                {field: 'stock', title: '库存数量', width: 100, align: 'center'},
                {field: 'status', title: '状态', width: 80, align: 'center', templet: function(d){
                    return d.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge">禁用</span>';
                }}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
        });
        
        // 搜索表单提交
        form.on('submit(search)', function(data) {
            tableIns.reload({
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 确定选择
        $('#btnConfirm').click(function() {
            var checkStatus = table.checkStatus('productTable');
            var data = checkStatus.data;
            if (data.length === 0) {
                layer.msg('请至少选择一个商品', {icon: 2});
                return;
            }
            
            // 调用父页面的添加子商品方法
            if (parent.addChildToParent) {
                parent.addChildToParent(data);
            } else {
                layer.msg('父页面未定义添加子商品方法', {icon: 2});
            }
        });
        
        // 取消按钮
        $('#btnCancel').click(function() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        });
        
        // BOM展开功能
        $('body').on('click', '.bom-toggle', function(e) {
            e.stopPropagation(); // 阻止事件冒泡，避免触发表格行选中
            var productId = $(this).data('product-id');
            var loadingIndex = layer.load(1); // 显示加载中
            
            // 加载BOM数据
            tool.get('/api/test/getBomInfo', {id: productId}, function(res) {
                layer.close(loadingIndex); // 关闭加载中
                
                if (res.code == 0 && res.data) {
                    var bomData = res.data;
                    var html = '<div style="padding: 15px;"><table class="bom-detail-table">';
                    html += '<thead><tr>';
                    html += '<th>序号</th>';
                    html += '<th>物料编码</th>';
                    html += '<th>物料名称</th>';
                    html += '<th>规格型号</th>';
                    html += '<th>单位</th>';
                    html += '<th>单位用量</th>';
                    html += '<th>当前库存</th>';
                    html += '<th>缺口数量</th>';
                    html += '</tr></thead><tbody>';
                    
                    if (bomData.items && bomData.items.length > 0) {
                        bomData.items.forEach(function(item, index) {
                            // 计算缺口 = 单位用量 - 当前库存
                            var stock = item.stock || 0;
                            var shortage = item.qty - stock;
                            
                            html += '<tr>';
                            html += '<td>' + (index + 1) + '</td>';
                            html += '<td>' + (item.material_code || '') + '</td>';
                            html += '<td>' + (item.material_name || '') + '</td>';
                            html += '<td>' + (item.product_specs || '') + '</td>';
                            html += '<td>' + (item.uom_name || '') + '</td>';
                            html += '<td>' + (item.qty || '0') + '</td>';
                            html += '<td>' + stock + '</td>';
                            html += '<td class="' + (shortage > 0 ? 'bom-shortage' : '') + '">' + 
                                   (shortage > 0 ? shortage.toFixed(2) : '0') + '</td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="8">暂无BOM数据</td></tr>';
                    }
                    
                    html += '</tbody></table></div>';
                    
                    // 使用layer.open创建弹出窗口
                    layer.open({
                        type: 1,
                        title: 'BOM清单详情',
                        area: ['80%', '600px'], // 设置弹窗宽高
                        shadeClose: true, // 点击遮罩关闭
                        maxmin: true, // 允许最大化最小化
                        content: html // 弹窗内容
                    });
                } else {
                    layer.msg('加载BOM数据失败: ' + (res.msg || '未知错误'), {icon: 2});
                }
            });
        });
    }
</script>
{/block}
<!-- /脚本 --> 