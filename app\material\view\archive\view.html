{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card">
		<div class="layui-card-header">
			<h3>物料档案详情</h3>
		</div>
		<div class="layui-card-body">
			<!-- 基础资料 -->
			<fieldset class="layui-elem-field layui-field-title">
				<legend style="color: #ff5722;">基础资料</legend>
			</fieldset>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料编号：</label>
						<span>{$detail.material_code}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料名称：</label>
						<span>{$detail.material_name}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料分类：</label>
						<span>{$detail.category.name|default='未分类'}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料来源：</label>
						<span>{$detail.material_source|default='-'}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md4">
					<div class="info-item">
						<label>型号：</label>
						<span>{$detail.model|default='-'}</span>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="info-item">
						<label>类别：</label>
						<span>{$detail.category|default='-'}</span>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="info-item">
						<label>颜色：</label>
						<span>{$detail.color|default='-'}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料等级：</label>
						<span>{$detail.material_level|default='-'}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>备注：</label>
						<span>{$detail.remark|default='-'}</span>
					</div>
				</div>
			</div>
			
			<!-- 价格信息 -->
			<fieldset class="layui-elem-field layui-field-title">
				<legend style="color: #ff5722;">价格信息</legend>
			</fieldset>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md3">
					<div class="info-item">
						<label>参考成本价：</label>
						<span>¥{$detail.reference_cost|default='0.00'}</span>
					</div>
				</div>
				<div class="layui-col-md3">
					<div class="info-item">
						<label>销售单价：</label>
						<span>¥{$detail.sales_price|default='0.00'}</span>
					</div>
				</div>
				<div class="layui-col-md3">
					<div class="info-item">
						<label>最低销售单价：</label>
						<span>¥{$detail.min_sales_price|default='0.00'}</span>
					</div>
				</div>
				<div class="layui-col-md3">
					<div class="info-item">
						<label>最高销售单价：</label>
						<span>¥{$detail.max_sales_price|default='0.00'}</span>
					</div>
				</div>
			</div>
			
			<!-- 单位信息 -->
			<fieldset class="layui-elem-field layui-field-title">
				<legend style="color: #ff5722;">单位信息</legend>
			</fieldset>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>基本单位：</label>
						<span>{$detail.base_unit|default='-'}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>默认仓库：</label>
						<span>{$detail.default_warehouse|default='-'}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>最小起订量：</label>
						<span>{$detail.min_order_qty|default='0'}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>最小包装量：</label>
						<span>{$detail.min_package_qty|default='0'}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>物料有效期：</label>
						<span>{$detail.material_validity|default='-'}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>存货计价方法：</label>
						<span>{$detail.cost_calculation|default='-'}</span>
					</div>
				</div>
			</div>
			
			<!-- 质检信息 -->
			<fieldset class="layui-elem-field layui-field-title">
				<legend style="color: #ff5722;">质检信息</legend>
			</fieldset>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>质检管理：</label>
						<span class="layui-badge {if condition='$detail.quality_management == 1'}layui-bg-green{else/}layui-bg-gray{/if}">
							{if condition='$detail.quality_management == 1'}启用{else/}禁用{/if}
						</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>免检设置：</label>
						<div class="quality-settings">
							{if condition='in_array("purchase_inspect", $detail.quality_settings)'}
							<span class="layui-badge layui-bg-red">采购入库检</span>
							{/if}
							{if condition='in_array("outsource_inspect", $detail.quality_settings)'}
							<span class="layui-badge layui-bg-red">外协入库检</span>
							{/if}
							{if condition='in_array("production_inspect", $detail.quality_settings)'}
							<span class="layui-badge layui-bg-red">生产入库检</span>
							{/if}
							{if condition='in_array("sales_inspect", $detail.quality_settings)'}
							<span class="layui-badge layui-bg-red">销售出库检</span>
							{/if}
						</div>
					</div>
				</div>
			</div>
			
			<!-- 系统信息 -->
			<fieldset class="layui-elem-field layui-field-title">
				<legend style="color: #ff5722;">系统信息</legend>
			</fieldset>
			
			<div class="layui-row layui-col-space15">
				<div class="layui-col-md6">
					<div class="info-item">
						<label>创建时间：</label>
						<span>{$detail.create_time}</span>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="info-item">
						<label>更新时间：</label>
						<span>{$detail.update_time|default='-'}</span>
					</div>
				</div>
			</div>
			
			<div class="layui-form-item" style="margin-top: 30px;">
				<div class="layui-input-block">
					<button type="button" class="layui-btn" onclick="parent.layer.closeAll()">关闭</button>
				</div>
			</div>
		</div>
	</div>
</div>

{/block}
<!-- /主体 -->

{block name="style"}
<style>
.info-item {
	margin-bottom: 15px;
	line-height: 38px;
}
.info-item label {
	font-weight: bold;
	color: #333;
	display: inline-block;
	width: 120px;
}
.info-item span {
	color: #666;
}
.quality-settings .layui-badge {
	margin-right: 5px;
}
</style>
{/block}