{extend name="../../base/view/common/base" /}
{block name="style"}
<form class="layui-form layui-form-pane" action="{:url('engineering/bom/add')}" method="post">
    <!-- 警告信息显示区域 -->
    {notempty name="warnings"}
    <div class="layui-form-item">
        <div class="layui-card">
            <div class="layui-card-header" style="background-color: #FFB800; color: #fff;">数据检查警告</div>
            <div class="layui-card-body">
                <div class="layui-bg-orange" style="padding: 10px;">
                    <ul>
                        {foreach $warnings as $warning}
                        <li><i class="layui-icon layui-icon-tips"></i> {$warning}</li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {/notempty}
    
    <div class="layui-form-item">
        <div class="layui-card">
            <div class="layui-card-header">BOM基本信息</div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品</label>
                            <div class="layui-input-block">
                                <select name="product_id" lay-filter="product_id" lay-verify="required" lay-search>
                                    <option value="">请选择产品</option>
                                    {foreach $product_list as $vo}
                                    <option value="{$vo.id}" {if isset($detail) && $detail.product_id==$vo.id}selected{/if}>{$vo.title}（{$vo.material_code}）</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                  
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">BOM编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="bom_code" value="{$detail.bom_code|default=''}" placeholder="不填写将自动生成唯一编号，也可手动输入" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">版本号</label>
                            <div class="layui-input-block">
                                <input type="text" name="version" value="{$detail.version|default='V1.0'}" placeholder="请输入版本号，默认V1.0" class="layui-input" lay-verify="required">
                                <div class="layui-form-mid layui-word-aux">只能包含字母、数字、下划线、破折号和点号，如V1.0</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">标准BOM</label>
                            <div class="layui-input-block">
                                <input type="radio" name="is_standard" value="1" title="是" {if isset($detail) && $detail.is_standard==1}checked{/if}>
                                <input type="radio" name="is_standard" value="0" title="否" {if !isset($detail) || $detail.is_standard==0}checked{/if}>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea name="remarks" placeholder="请输入备注" class="layui-textarea">{$detail.remarks|default=''}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-form-item text-center">
        <input type="hidden" name="id" value="{$detail.id|default=''}">
        <button class="layui-btn" lay-filter="formSubmit" lay-submit>保存</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}

{block name="js"}
<script>
       const delay_num = 30;
	const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','layer'];
	function gouguInit() {
    layui.use(['form', 'layer'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        
        // 数据预加载检查
        function checkDataPreloaded() {
            // 检查产品下拉列表是否有选项
            var hasProducts = $('select[name="product_id"] option').length > 1; // 除了"请选择"之外是否有其他选项
            
            if (!hasProducts) {
                layer.msg('没有可用的产品数据，请先添加产品', {icon: 2, time: 3000});
                return false;
            }
            
            return true;
        }
        
        // 页面加载完成后执行检查
        $(function() {
            checkDataPreloaded();
        });
        
        // 表单提交事件
        form.on('submit(formSubmit)', function (data) {
            // 提交前再次检查数据是否预加载
            if (!checkDataPreloaded()) {
                return false;
            }
            
            // 验证产品信息是否完整
            if (!data.field.product_id) {
                layer.msg('请选择产品', {icon: 2});
                return false;
            }
            
           
            
            // 如果BOM编号是手动输入的非空值，提示用户可能存在重复风险
            var bomCode = data.field.bom_code.trim();
            if (bomCode && !bomCode.startsWith('BOM')) {
                var confirmIndex = layer.confirm('您手动输入的BOM编号可能与现有记录冲突，建议使用系统自动生成。确定要继续吗？', {
                    btn: ['继续', '清空编号并提交'] //按钮
                }, function(){
                    // 用户确认继续
                    layer.close(confirmIndex);
                    submitForm(data);
                }, function(){
                    // 用户选择清空并使用自动生成
                    $('input[name="bom_code"]').val(''); // 清空页面上的输入框
                    data.field.bom_code = ''; // 更新提交数据
                    layer.close(confirmIndex);
                    submitForm(data);
                });
                return false;
            }
            
            // 正常提交表单
            submitForm(data);
            return false;
        });
        
        // 提交表单函数
        function submitForm(data) {
            // 如果BOM编号为空，显示提示信息
            if (!data.field.bom_code) {
                layer.msg('将使用系统自动生成的BOM编号', {icon: 6, time: 1500});
            }
            
            var loading = layer.load(2);
            $.ajax({
                url: data.form.action,
                type: 'post',
                data: data.field,
                dataType: 'json',
                success: function (res) {
                    layer.close(loading);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                            // 获取新增BOM的ID
                            var bomId = res.data.aid;
                            
                            // 刷新父页面表格
                            if (parent.layui && parent.layui.table) {
                                parent.layui.table.reload('dataTable');
                            }
                            
                            // 关闭当前窗口
                            var index = parent.layer.getFrameIndex(window.name); 
                            parent.layer.close(index);
                            
                            // 如果有BOM ID，打开子项管理页面
                            if (bomId) {
                                // 延迟一点打开新窗口，确保当前窗口已经完全关闭
                                setTimeout(function() {
                                    parent.layer.open({
                                        type: 2,
                                        title: 'BOM子项管理',
                                        content: '{:url("engineering/bom/items")}?bom_id=' + bomId,
                                        area: ['90%', '90%'],
                                        maxmin: true,
                                        shade: 0.3,
                                        moveOut: true
                                    });
                                }, 100);
                            }
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('请求异常', {icon: 2});
                }
            });
        }
        
        // 产品选择事件
        form.on('select(product_id)', function (data) {
            var productId = data.value;
            if (!productId) {
                $('input[name="product_name"]').val('');
                $('input[name="product_code"]').val('');
                return;
            }
            
            // 只有当产品名称为空时，才自动获取填充
            var currentProductName = $('input[name="product_name"]').val();
            var shouldFetchName = !currentProductName || currentProductName.trim() === '';
            
            $.ajax({
                url: '{:url("engineering/product/getById")}',
                type: 'post',
                data: {id: productId},
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0 && res.data) {
                        // 如果产品名称为空，自动填充
                        if (shouldFetchName) {
                            $('input[name="product_name"]').val(res.data.title);
                        }
                        $('input[name="product_code"]').val(res.data.material_code);
                    }
                }
            });
        });
    });
    }
</script>
{/block} 