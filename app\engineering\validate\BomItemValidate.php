<?php
declare (strict_types = 1);
namespace app\engineering\validate;

use think\Validate;

/**
 * BOM子项表单验证器
 * Class BomItemValidate
 * @package app\engineering\validate
 */
class BomItemValidate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'bom_id' => 'require|number',
        'material_id' => 'require|number',
        'material_name' => 'require',
        'material_code' => 'require',
        'qty' => 'require|float|gt:0',
        'loss_rate' => 'float|egt:0|elt:100',
        'remark' => 'max:500',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'bom_id.require' => 'BOM ID不能为空',
        'bom_id.number' => 'BOM ID必须为数字',
        'material_id.require' => '物料不能为空',
        'material_id.number' => '物料ID必须为数字',
        'material_name.require' => '物料名称不能为空',
        'material_code.require' => '物料编码不能为空',
        'qty.require' => '用量不能为空',
        'qty.float' => '用量必须为数字',
        'qty.gt' => '用量必须大于0',
        'loss_rate.float' => '损耗率必须为数字',
        'loss_rate.egt' => '损耗率不能小于0',
        'loss_rate.elt' => '损耗率不能大于100',
        'remark.max' => '备注不能超过500个字符',
    ];

    /**
     * 定义验证场景
     * 格式：'场景名' => ['字段名1','字段名2',...]
     *
     * @var array
     */
    protected $scene = [
        'add' => ['bom_id', 'material_id', 'material_name', 'material_code', 'qty', 'loss_rate', 'remark'],
        'edit' => ['material_id', 'material_name', 'material_code', 'qty', 'loss_rate', 'remark'],
    ];
} 