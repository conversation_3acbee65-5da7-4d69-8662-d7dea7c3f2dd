<?php
namespace app\material\controller;

use app\base\BaseController;
use app\material\model\Category as CategoryModel;
use think\exception\ValidateException;
use think\facade\View;

class Category extends BaseController
{
    /**
     * 物料分类列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            if (!empty($param['keywords'])) {
                $where[] = ['name', 'like', '%' . $param['keywords'] . '%'];
            }
            
            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $order = empty($param['order']) ? 'sort asc, id asc' : $param['field'] . ' ' . $param['order'];
            
            $data = CategoryModel::where($where)
                ->order($order)
                ->paginate(['list_rows' => $rows, 'page' => $param['page']])
                ->each(function($item) {
                    $item->status_name = $item->status == 1 ? '启用' : '禁用';
                    return $item;
                });
                
            return table_assign(0, '', $data);
        } else {
            return view();
        }
    }
    
    /**
     * 添加/编辑物料分类
     */
    public function add()
    {
        $param = get_params();
        if (isset($param['category_name'])){
            $param['name']= $param['category_name'];
        }
       
        if (request()->isPost()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                $param['update_time'] = time();
                $res = CategoryModel::where('id', $param['id'])->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                    return to_assign(0, "物料分类编辑成功");
                } else {
                    return to_assign(1, "物料分类编辑失败");
                }
            } else {
                $param['create_time'] = time();
                $insertId = CategoryModel::strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                    return to_assign(0, "物料分类添加成功", ['aid' => $insertId]);
                } else {
                    return to_assign(1, "物料分类添加失败");
                }
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $pid = isset($param['pid']) ? $param['pid'] : 0;
            $detail = [];
            if ($id > 0) {
                $detail = CategoryModel::where('id', $id)->find();
                if (empty($detail)) {
                    throw new \think\exception\HttpException(404, '物料分类不存在');
                }
                $detail = $detail->toArray();
                View::assign('detail', $detail);
            } else {
                // 如果是添加下级分类，设置父级ID
                if ($pid > 0) {
                    $detail['pid'] = $pid;
                    View::assign('detail', $detail);
                }
            }
            
            // 获取所有分类用于父级选择
            $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select()->toArray();
            View::assign('categories', $categories);
            View::assign('id', $id);
            View::assign('pid', $pid);
            return view();
        }
    }
    
    /**
     * 删除物料分类
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }
        
        // 检查是否有子分类
        $hasChildren = CategoryModel::where('pid', $id)->count();
        if ($hasChildren > 0) {
            return to_assign(1, "该分类下存在子分类，无法删除");
        }
        
        // 检查是否有物料使用该分类
        $hasArchive = \app\material\model\Archive::where('category_id', $id)->count();
        if ($hasArchive > 0) {
            return to_assign(1, "该分类下存在物料档案，无法删除");
        }
        
        $detail = CategoryModel::where('id', $id)->find();
        if (empty($detail)) {
            return to_assign(1, "物料分类不存在");
        }
        
        $res = CategoryModel::destroy($id);
        if ($res) {
            add_log('delete', $id);
            return to_assign(0, "物料分类删除成功");
        } else {
            return to_assign(1, "物料分类删除失败");
        }
    }
    
    /**
     * 获取分类树形结构
     */
    public function getTree()
    {
        $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select()->toArray();
        $tree = $this->buildTree($categories);
        return to_assign(0, '', $tree);
    }
    
    /**
     * 构建树形结构
     */
    private function buildTree($data, $pid = 0)
    {
        $tree = [];
        foreach ($data as $item) {
            if ($item['pid'] == $pid) {
                $children = $this->buildTree($data, $item['id']);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }
    
    /**
     * 更新分类状态
     */
    public function updateStatus()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $status = isset($param['status']) ? $param['status'] : 0;
        
        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }
        
        $res = CategoryModel::where('id', $id)->update(['status' => $status, 'update_time' => time()]);
        if ($res) {
            add_log('update_status', $id, ['status' => $status]);
            return to_assign(0, "状态更新成功");
        } else {
            return to_assign(1, "状态更新失败");
        }
    }
}
