<?php

namespace app\material\controller;

use app\base\BaseController;
use app\material\model\Archive as ArchiveModel;
use app\material\model\Category as CategoryModel;
use app\material\validate\Archive as ArchiveValidate;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Archive extends BaseController
{
    /**
     * 物料档案列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 分类筛选
            if (!empty($param['category_id'])) {
                $where[] = ['category_id', '=', $param['category_id']];
            }
            
            // 关键字搜索
            if (!empty($param['keywords'])) {
                $where[] = ['material_name|material_code', 'like', '%' . $param['keywords'] . '%'];
            }
            
            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $order = empty($param['order']) ? 'id desc' : $param['field'] . ' ' . $param['order'];
            
            $data = ArchiveModel::where($where)
                ->with(['category'])
                ->order($order)
                ->paginate(['list_rows' => $rows, 'page' => $param['page']])
                ->each(function($item) {
                    $item->category_name = $item->category ? $item->category->name : '';
                    return $item;
                });
                
            return table_assign(0, '', $data);
        } else {
            // 获取分类数据并构建树形结构
            $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select()->toArray();
            $categoryTree = $this->buildCategoryTree($categories);
            View::assign('categoryTree', $categoryTree);
            return view();
        }
    }
    
    /**
     * 添加/编辑物料档案
     */
    public function add()
    {
        $param = get_params();
        if (request()->isPost()) {
            // 调试信息
            \think\facade\Log::info('Archive add/edit params: ' . json_encode($param));
  
            if (!empty($param['id']) && $param['id'] > 0) {
                
                // 编辑时，如果没有物料编号或启用了自动编号，则生成编号
                if ((empty($param['material_code']) || $param['material_code'] === 'AUTO_GENERATE') && !empty($param['auto_material_code'])) {
                    $param['material_code'] = $this->generateMaterialCode();
                    \think\facade\Log::info('Generated material code for edit: ' . $param['material_code']);
                }

                // 验证物料编号
                if (empty($param['material_code'])) {
                    \think\facade\Log::error('Material code is empty for edit');
                    return to_assign(1, "物料编号不能为空");
                }

                try {
                    // 设置默认值，避免验证失败
                    $param['base_unit'] = $param['base_unit'] ?? 0;
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;
                    
                    validate(ArchiveValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }

                $param['update_time'] = time();
                
                // 开启事务
                Db::startTrans();
                try {
                    // 设置默认值，避免验证失败
                    $param['base_unit'] = $param['base_unit'] ?? 0;
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;
                    
                    validate(ArchiveValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }
                    
                    $materialId = $param['id'];
                    
                    // 删除旧的供应商价格数据
                    Db::name('oa_material_supplier_price')->where('material_id', $materialId)->delete();
                    
                    // 处理新的供应商价格数据
                    if (!empty($param['supplier_prices']) && is_array($param['supplier_prices'])) {
                        foreach ($param['supplier_prices'] as $supplierPrice) {
                            if (!empty($supplierPrice['supplier_id'])) {
                                $supplierPriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $supplierPrice['supplier_id'],
                                    'supplier_code' => $supplierPrice['supplier_code'] ?? '',
                                    'priority' => $supplierPrice['priority'] ?? '',
                                    'min_qty' => $supplierPrice['min_qty'] ?? 0,
                                    'max_qty' => $supplierPrice['max_qty'] ?? 0,
                                    'tax_price' => $supplierPrice['tax_price'] ?? 0,
                                    'tax_rate' => $supplierPrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $supplierPrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('oa_material_supplier_price')->insert($supplierPriceData);
                            }
                        }
                    }
                    
                    // 删除旧的外协价格数据
                    Db::name('oa_material_outsource_price')->where('material_id', $materialId)->delete();
                    
                    // 处理新的外协价格数据
                    if (!empty($param['outsource_prices']) && is_array($param['outsource_prices'])) {
                        foreach ($param['outsource_prices'] as $outsourcePrice) {
                            if (!empty($outsourcePrice['supplier_id'])) {
                                $outsourcePriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $outsourcePrice['supplier_id'],
                                    'outsource_code' => $outsourcePrice['outsource_code'] ?? '',
                                    'priority' => $outsourcePrice['priority'] ?? '',
                                    'min_qty' => $outsourcePrice['min_qty'] ?? 0,
                                    'max_qty' => $outsourcePrice['max_qty'] ?? 0,
                                    'tax_price' => $outsourcePrice['tax_price'] ?? 0,
                                    'tax_rate' => $outsourcePrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $outsourcePrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('oa_material_outsource_price')->insert($outsourcePriceData);
                            }
                        }
                    }
                    
                    // 提交事务
                    Db::commit();
                    add_log('edit', $param['id'], $param);
                    return to_assign(0, "物料档案编辑成功");
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    \think\facade\Log::error('Archive edit error: ' . $e->getMessage());
                    return to_assign(1, "物料档案编辑失败：" . $e->getMessage());
                }
            } else {
               
                // 新增时，如果没有物料编号或启用了自动编号，则生成编号
                if ((empty($param['material_code']) || $param['material_code'] === 'AUTO_GENERATE') && !empty($param['auto_material_code'])) {
                    $param['material_code'] = $this->generateMaterialCode();
                    \think\facade\Log::info('Generated material code for add: ' . $param['material_code']);
                }
 
                // 验证物料编号
                if (empty($param['material_code'])) {
                    \think\facade\Log::error('Material code is empty for add. Params: ' . json_encode([
                        'material_code' => $param['material_code'] ?? 'not set',
                        'auto_material_code' => $param['auto_material_code'] ?? 'not set'
                    ]));
                    return to_assign(1, "物料编号不能为空");
                }

                try {
                    // 设置默认值，避免验证失败
                    $param['base_unit'] = $param['base_unit'] ?? 0;
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;
                    
                    validate(ArchiveValidate::class)->scene('add')->check($param);
                     
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }

                $param['create_time'] = time();
                $param['update_time'] = time();
                
                // 开启事务
                Db::startTrans();
                try {
                    // 创建物料档案
                    $res = ArchiveModel::strict(false)->field(true)->create($param);
                    if (!$res) {
                        throw new \Exception('物料档案创建失败');
                    }
                    
                    $materialId = $res->id;
                    
                    // 处理供应商价格数据
                    if (!empty($param['supplier_prices']) && is_array($param['supplier_prices'])) {
                        foreach ($param['supplier_prices'] as $supplierPrice) {
                            if (!empty($supplierPrice['supplier_id'])) {
                                $supplierPriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $supplierPrice['supplier_id'],
                                    'supplier_code' => $supplierPrice['supplier_code'] ?? '',
                                    'priority' => $supplierPrice['priority'] ?? '',
                                    'min_qty' => $supplierPrice['min_qty'] ?? 0,
                                    'max_qty' => $supplierPrice['max_qty'] ?? 0,
                                    'tax_price' => $supplierPrice['tax_price'] ?? 0,
                                    'tax_rate' => $supplierPrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $supplierPrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('oa_material_supplier_price')->insert($supplierPriceData);
                            }
                        }
                    }
                    
                    // 处理外协价格数据
                    if (!empty($param['outsource_prices']) && is_array($param['outsource_prices'])) {
                        foreach ($param['outsource_prices'] as $outsourcePrice) {
                            if (!empty($outsourcePrice['supplier_id'])) {
                                $outsourcePriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $outsourcePrice['supplier_id'],
                                    'outsource_code' => $outsourcePrice['outsource_code'] ?? '',
                                    'priority' => $outsourcePrice['priority'] ?? '',
                                    'min_qty' => $outsourcePrice['min_qty'] ?? 0,
                                    'max_qty' => $outsourcePrice['max_qty'] ?? 0,
                                    'tax_price' => $outsourcePrice['tax_price'] ?? 0,
                                    'tax_rate' => $outsourcePrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $outsourcePrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('oa_material_outsource_price')->insert($outsourcePriceData);
                            }
                        }
                    }
                    
                    // 提交事务
                    Db::commit();
                    add_log('add', $materialId, $param);
                    return to_assign(0, "物料档案添加成功");
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    \think\facade\Log::error('Archive add error: ' . $e->getMessage());
                    return to_assign(1, "物料档案添加失败：" . $e->getMessage());
                }
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = [];
            if ($id > 0) {
                $detail = ArchiveModel::where('id', $id)->find();
                if (empty($detail)) {
                    throw new \think\exception\HttpException(404, '物料档案不存在');
                }
                $detail = $detail->toArray();
            }
            
            // 获取物料分类
            $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select()->toArray();

            // 获取供应商数据
            $suppliers = Db::name('purchase_supplier')->where('status', 1)->field('id,code,name')->order('id desc')->select()->toArray();

            // 获取仓库数据
            $warehouses = Db::name('warehouse')->where('status', 1)->field('id,code,name')->order('is_default desc, id asc')->select()->toArray();

            // 获取单位数据
            $units = Db::name('unit')->where('status', 1)->field('id,name,precision,type')->order('id asc')->select()->toArray();

            View::assign('detail', $detail);
            View::assign('categories', $categories);
            View::assign('suppliers', $suppliers);
            View::assign('warehouses', $warehouses);
            View::assign('units', $units);
            View::assign('id', $id);
            return view();
        }
    }

    /**
     * 查看物料档案详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = ArchiveModel::where('id', $id)->with(['category'])->find();
        if (empty($detail)) {
            throw new \think\exception\HttpException(404, '物料档案不存在');
        }
        
        $detail = $detail->toArray();
        View::assign('detail', $detail);
        return view();
    }
    
    /**
     * 删除物料档案
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }
        
        $detail = ArchiveModel::where('id', $id)->find();
        if (empty($detail)) {
            return to_assign(1, "物料档案不存在");
        }
        
        $res = ArchiveModel::destroy($id);
        if ($res) {
            add_log('delete', $id);
            return to_assign(0, "物料档案删除成功");
        } else {
            return to_assign(1, "物料档案删除失败");
        }
    }
    
    /**
     * 获取分类列表
     */
    public function getCategoryList()
    {
        $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select();
        return json(['code' => 0, 'data' => $categories]);
    }
    
    /**
     * 构建分类树形结构
     */
    private function buildCategoryTree($categories, $pid = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['pid'] == $pid) {
                $children = $this->buildCategoryTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                    $category['has_children'] = true;
                } else {
                    $category['has_children'] = false;
                }
                $tree[] = $category;
            }
        }
        return $tree;
    }
    
    /**
     * 导入物料档案
     */
    public function import()
    {
        if (request()->isPost()) {
            $param = get_params();
            // 处理导入逻辑
            return to_assign(0, "导入成功");
        } else {
            return view();
        }
    }
    
    /**
     * 导出物料档案
     */
    public function export()
    {
        $param = get_params();
        $where = [];
        
        if (!empty($param['category_id'])) {
            $where[] = ['category_id', '=', $param['category_id']];
        }
        
        if (!empty($param['keywords'])) {
            $where[] = ['material_name|material_code', 'like', '%' . $param['keywords'] . '%'];
        }
        
        $data = ArchiveModel::where($where)->with(['category'])->select()->toArray();
        
        // 导出Excel逻辑
        return to_assign(0, "导出成功");
    }
    
    /**
     * 批量删除
     */
    public function batchDelete()
    {
        $param = get_params();
        $ids = isset($param['ids']) ? $param['ids'] : '';
        if (empty($ids)) {
            return to_assign(1, "请选择要删除的物料档案");
        }

        $ids = explode(',', $ids);
        $res = ArchiveModel::destroy($ids);
        if ($res) {
            add_log('batch_delete', 0, ['ids' => $ids]);
            return to_assign(0, "批量删除成功");
        } else {
            return to_assign(1, "批量删除失败");
        }
    }

    /**
     * 生成物料编号
     */
    private function generateMaterialCode()
    {
        $prefix = 'WL'; // 物料前缀
        $date = date('Ymd'); // 当前日期

        // 查询当天最大的编号
        $maxCode = Db::name('material_archive')
            ->where('material_code', 'like', $prefix . $date . '%')
            ->max('material_code');

        if ($maxCode) {
            // 提取序号部分
            $sequence = intval(substr($maxCode, -4)) + 1;
        } else {
            $sequence = 1;
        }

        // 生成新编号：WL + 日期 + 4位序号
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
