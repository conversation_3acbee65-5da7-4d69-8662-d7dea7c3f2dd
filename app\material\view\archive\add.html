{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<style>
.material-form-container {
    background: #f5f6fa;
    min-height: 100vh;
    padding: 20px;
}

.material-card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;
    margin-bottom: 16px;
}

.section-header {
    background: #fafafa;
    color: #262626;
    padding: 12px 20px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
}

.section-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #1890ff;
}

.section-content {
    padding: 20px;
}

.form-row {
    margin-bottom: 16px;
}

.layui-form-label {
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 2px 0 0 2px;
    color: #262626;
    font-weight: 500;
    font-size: 13px;
}

.layui-form-label.required::after {
    content: '*';
    color: #ff4d4f;
    margin-left: 4px;
}

.layui-input, .layui-select, .layui-textarea {
    border: 1px solid #d9d9d9;
    border-radius: 0 2px 2px 0;
    transition: all 0.2s ease;
    font-size: 13px;
}

.layui-input:focus, .layui-select:focus, .layui-textarea:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.upload-area {
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.upload-area:hover {
    border-color: #1890ff;
    background: #f0f8ff;
}

.price-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.price-item {
    background: #fafafa;
    padding: 16px;
    border-radius: 4px;
    border-left: 3px solid #1890ff;
    border: 1px solid #e8e8e8;
}

/* 表格样式优化 */
.layui-table th {
    background: #fafafa !important;
    color: #262626 !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #e8e8e8 !important;
    font-size: 13px !important;
}

.layui-table tbody tr:hover {
    background: #f0f8ff !important;
}

.layui-table td {
    border-color: #f0f0f0 !important;
    font-size: 13px !important;
}

/* 按钮样式优化 */
.layui-btn {
    border-radius: 2px;
    font-size: 13px;
    transition: all 0.2s ease;
}

.layui-btn:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.layui-btn-normal {
    background-color: #1890ff;
    border-color: #1890ff;
}

.layui-btn-normal:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

/* 标签页优化 */
.layui-tab-title li {
    background: #fafafa;
    border-radius: 2px 2px 0 0;
    margin-right: 2px;
    border: 1px solid #e8e8e8;
    border-bottom: none;
    font-size: 13px;
}

.layui-tab-title .layui-this {
    background: #fff;
    color: #1890ff !important;
    border-color: #1890ff;
    border-bottom: 2px solid #1890ff;
}

/* 复选框和单选框样式 */
.layui-form-checkbox[lay-skin="primary"] i {
    border-color: #1890ff;
}

.layui-form-checked[lay-skin="primary"] i {
    background-color: #1890ff;
    border-color: #1890ff;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .price-grid {
        grid-template-columns: 1fr;
    }

    .material-form-container {
        padding: 10px;
    }

    .section-content {
        padding: 15px;
    }
}
</style>

<div class="material-form-container">
	<div class="material-card">
		<form class="layui-form" lay-filter="addForm" id="addForm">
			{if condition="$id > 0"}
			<input type="hidden" name="id" value="{$id}" />
			{/if}

			<!-- 基础资料 -->
			<div class="section-header">
				基础资料
			</div>
			<div class="section-content">
				
				<div class="layui-row layui-col-space20 form-row">
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label required">物料编号</label>
							<div class="layui-input-block">
								<div style="position: relative; display: flex; align-items: center;">
									<input type="text" name="material_code" value="{$detail.material_code ?? ''}" placeholder="请输入物料编号" class="layui-input" lay-verify="required" style="flex: 1; margin-right: 10px;" />
									<div style="display: flex; align-items: center; background: #fff; border: 1px solid #e6e6e6; padding: 8px 12px; border-radius: 2px; white-space: nowrap; cursor: pointer;" id="auto_code_container">
										<input type="checkbox" name="auto_material_code" value="1" id="auto_material_code" lay-filter="auto_material_code" style="margin-right: 5px; cursor: pointer;" />
										<label for="auto_material_code" style="margin: 0; cursor: pointer; font-size: 12px; color: #666; user-select: none;">使用系统编号</label>
									</div>
								</div>
								<div class="layui-form-mid layui-word-aux" style="color: #999; font-size: 12px; margin-top: 5px;">
									<i class="layui-icon layui-icon-tips"></i> 物料唯一标识码
								</div>
							</div>
						</div>
					</div>
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label required">物料名称</label>
							<div class="layui-input-block">
								<input type="text" name="material_name" value="{$detail.material_name ?? ''}" placeholder="请输入物料名称" class="layui-input" lay-verify="required" />
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-row layui-col-space20 form-row">
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label required">物料分类</label>
							<div class="layui-input-block">
								<select name="category_id" lay-verify="required">
									<option value="">请选择分类</option>
									{volist name="categories" id="category"}
									<option value="{$category.id}" {if condition="isset($detail['category_id']) && $detail['category_id'] == $category.id"}selected{/if}>{$category.name}</option>
									{/volist}
								</select>
							</div>
						</div>
					</div>
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label required">物料来源</label>
							<div class="layui-input-block">
								<select name="material_source">
									<option value="自购" {if condition="isset($detail['material_source']) && $detail['material_source'] == '自购'"}selected{/if}>自购</option>
									<option value="加工" {if condition="isset($detail['material_source']) && $detail['material_source'] == '加工'"}selected{/if}>加工</option>
									<option value="客供" {if condition="isset($detail['material_source']) && $detail['material_source'] == '客供'"}selected{/if}>客供</option>
                                    	<option value="委外供料" {if condition="isset($detail['material_source']) && $detail['material_source'] == '委外供料'"}selected{/if}>委外供料</option>
								</select>
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-row layui-col-space15">
					<div class="layui-col-md4">
						<div class="layui-form-item">
							<label class="layui-form-label">型号</label>
							<div class="layui-input-block">
							<input type="text" name="model" value="{$detail.model ?? ''}" placeholder="请输入型号" class="layui-input" />
							</div>
						</div>
					</div>
					<div class="layui-col-md4">
						<div class="layui-form-item">
							<label class="layui-form-label">类别</label>
							<div class="layui-input-block">
								<select name="category">
									<option value="">请选择</option>
									<option value="主料" {if condition="isset($detail['category']) && $detail['category'] == '主料'"}selected{/if}>主料</option>
									<option value="辅料" {if condition="isset($detail['category']) && $detail['category'] == '辅料'"}selected{/if}>辅料</option>
									 
								</select>
							</div>
						</div>
					</div>
					<div class="layui-col-md4">
						<div class="layui-form-item">
							<label class="layui-form-label">颜色</label>
							<div class="layui-input-block">
								<input type="text" name="color" value="{$detail.color ?? ''}" placeholder="请输入颜色" class="layui-input" />
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-row layui-col-space15">
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label">物料等级</label>
							<div class="layui-input-block">
								<select name="material_level">
									<option value="">请选择</option>
									<option value="A" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'A'"}selected{/if}>A</option>
									<option value="B" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'B'"}selected{/if}>B</option>
									<option value="C" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'C'"}selected{/if}>C</option>
								</select>
							</div>
						</div>
					</div>
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label">备注</label>
							<div class="layui-input-block">
								<textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$detail.remark ?? ''}</textarea>
								<div class="layui-form-mid layui-word-aux">0 / 100</div>
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-row layui-col-space15">
				 
					<div class="layui-col-md6">
						<div class="layui-form-item">
							<label class="layui-form-label">附件</label>
							<div class="layui-input-block">
								<button type="button" class="layui-btn layui-btn-primary" id="uploadBtn">
									<i class="layui-icon layui-icon-upload"></i> 上传文件
								</button>
							</div>
						</div>
					</div>
				</div>
				
				<!-- CAD图纸 -->
				<div class="layui-form-item">
					<label class="layui-form-label">CAD图纸</label>
					<div class="layui-input-block">
						<div class="upload-area" style="width: 120px; height: 120px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer;">
							<i class="layui-icon layui-icon-upload" style="font-size: 24px; color: #1890ff; margin-bottom: 6px;"></i>
							<span style="color: #666; font-size: 12px;">点击上传</span>
							<span style="color: #999; font-size: 11px; margin-top: 2px;">DWG/PDF/JPG</span>
						</div>
					</div>
				</div>

			</div>
		</div>

		<!-- 价格信息 -->
	<div class="material-card">
		<div class="section-header">
			价格信息
		</div>
		<div class="section-content">
			<div class="price-grid">
				<div class="price-item">
					<div class="layui-form-item">
						<label class="layui-form-label required">参考成本价</label>
						<div class="layui-input-block">
							<input type="number" name="reference_cost" value="{$detail.reference_cost ?? '56'}" placeholder="请输入参考成本价" class="layui-input" step="0.01" />
							<div class="layui-form-mid layui-word-aux">基本单位</div>
						</div>
					</div>
				</div>
				<div class="price-item">
					<div class="layui-form-item">
						<label class="layui-form-label">销售单价（含税）</label>
						<div class="layui-input-block">
							<input type="number" name="sales_price" value="{$detail.sales_price ?? ''}" placeholder="请输入销售单价" class="layui-input" step="0.01" />
							<div class="layui-form-mid layui-word-aux">基本单位</div>
						</div>
					</div>
				</div>
				<div class="price-item">
					<div class="layui-form-item">
						<label class="layui-form-label">最低销售单价</label>
						<div class="layui-input-block">
							<input type="number" name="min_sales_price" value="{$detail.min_sales_price ?? ''}" placeholder="请输入最低销售单价" class="layui-input" step="0.01" />
							<div class="layui-form-mid layui-word-aux">含税 - 基本单位</div>
						</div>
					</div>
				</div>
				<div class="price-item">
					<div class="layui-form-item">
						<label class="layui-form-label">最高销售单价</label>
						<div class="layui-input-block">
							<input type="number" name="max_sales_price" value="{$detail.max_sales_price ?? ''}" placeholder="请输入最高销售单价" class="layui-input" step="0.01" />
							<div class="layui-form-mid layui-word-aux">含税 - 基本单位</div>
						</div>
					</div>
				</div>
			</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">是否启用价格区间校验</label>
					<div class="layui-input-block">
					<input type="checkbox" name="is_price_range_check" value="1" {if condition="isset($detail['is_price_range_check']) && $detail['is_price_range_check'] == 1"}checked{/if} title="默认" />
					</div>
				</div>

		</div>

		<!-- 质检信息 -->
	<div class="material-card">
		<div class="section-header">
			质检信息
		</div>
		<div class="section-content">
				
				<div class="layui-form-item">
					<label class="layui-form-label">质检管理</label>
					<div class="layui-input-block">
						<input type="checkbox" name="quality_management" value="1" lay-skin="switch" lay-text="启用|禁用" lay-filter="quality_management" {if condition="isset($detail['quality_management']) && $detail['quality_management'] == 1"}checked{/if} />
					</div>
				</div>
				
				<div class="layui-form-item quality-exempt-section" style="display: none;">
					<label class="layui-form-label">免检设置</label>
					<div class="layui-input-block">
						<input type="checkbox" name="quality_exempt" value="1" lay-skin="switch" lay-text="关闭|开启" lay-filter="quality_exempt" />
					</div>
				</div>
				
				<div class="layui-form-item quality-options-section" style="display: none;">
					<label class="layui-form-label"></label>
					<div class="layui-input-block">
						<div class="quality-check-options">
							<div class="check-option">
								<input type="checkbox" name="quality_settings[]" value="purchase_inspect" title="采购入库检" />
								<span style="margin-left: 8px; color: #666;">采购入库检</span>
							</div>
							<div class="check-option">
								<input type="checkbox" name="quality_settings[]" value="outsource_inspect" title="外协入库检" />
								<span style="margin-left: 8px; color: #666;">外协入库检</span>
							</div>
							<div class="check-option">
								<input type="checkbox" name="quality_settings[]" value="production_inspect" title="生产入库检" />
								<span style="margin-left: 8px; color: #666;">生产入库检</span>
							</div>
							<div class="check-option">
								<input type="checkbox" name="quality_settings[]" value="sales_inspect" title="销售出库检" />
								<span style="margin-left: 8px; color: #666;">销售出库检</span>
							</div>
						</div>
					</div>
				</div>
				
		</div>
	</div>

	<!-- 详细信息、物料单价、外协单价 标签页 -->
	<div class="material-card">
		<div class="layui-tab layui-tab-brief" lay-filter="materialTabs" style="margin: 0;">
			<ul class="layui-tab-title" style="background: #fafafa; margin: 0; padding: 0 16px; border-bottom: 1px solid #e8e8e8;">
				<li class="layui-this" style="margin-right: 4px;">
					详细信息
				</li>
				<li style="margin-right: 4px;">
					物料单价
				</li>
				<li>
					外协单价
				</li>
			</ul>
			<div class="layui-tab-content" style="padding: 20px;">
						<!-- 详细信息 -->
						<div class="layui-tab-item layui-show">
							<div style="background: #fafafa; color: #262626; padding: 12px 16px; margin: -20px -20px 16px -20px; font-weight: 600; border-bottom: 1px solid #e8e8e8; font-size: 13px;">
								单位信息
							</div>
							
							<div class="layui-row layui-col-space15">
								<div class="layui-col-md6">
									<div class="layui-form-item">
										<label class="layui-form-label required">基本单位</label>
										<div class="layui-input-block">
											<select name="base_unit" lay-verify="required" lay-filter="base_unit">
												<option value="">请选择基本单位</option>
												{volist name="units" id="unit"}
												<option value="{$unit.id}" {if condition="isset($detail['base_unit']) && $detail['base_unit'] == $unit.id"}selected{/if}>{$unit.name}</option>
												{/volist}
											</select>
										</div>
									</div>
								</div>
								<div class="layui-col-md6">
									<div class="layui-form-item">
										<label class="layui-form-label">默认仓库</label>
										<div class="layui-input-block">
											<select name="default_warehouse">
												<option value="">请选择仓库</option>
												{volist name="warehouses" id="warehouse"}
												<option value="{$warehouse.id}" {if condition="isset($detail['default_warehouse']) && $detail['default_warehouse'] == $warehouse.id"}selected{/if}>{$warehouse.name}</option>
												{/volist}
											</select>
										</div>
									</div>
								</div>
							</div>
							
							<div class="layui-row layui-col-space15">
								<div class="layui-col-md6">
									<div class="layui-form-item">
										<label class="layui-form-label">最小起订量</label>
										<div class="layui-input-block">
											<input type="number" name="min_order_qty" value="{$detail.min_order_qty ?? ''}" placeholder="请输入最小起订量" class="layui-input" step="0.01" />
										</div>
									</div>
								</div>
								<div class="layui-col-md6">
									<div class="layui-form-item">
										<label class="layui-form-label">最小包装量</label>
										<div class="layui-input-block">
											<input type="number" name="min_package_qty" value="{$detail.min_package_qty ?? ''}" placeholder="请输入最小包装量" class="layui-input" step="0.01" />
										</div>
									</div>
								</div>
							</div>
							
						</div>
						
						<!-- 物料单价 -->
						<div class="layui-tab-item">
							<!-- 供应商价格表格 -->
							<div style="background: #fafafa; padding: 16px; border-radius: 4px; margin-bottom: 16px; border: 1px solid #e8e8e8;">
								<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
									<h4 style="margin: 0; color: #262626; font-size: 14px; font-weight: 600;">
										供应商价格管理
									</h4>
									<button type="button" class="layui-btn layui-btn-normal layui-btn-sm" onclick="addSupplierPriceRow()">
										<i class="layui-icon layui-icon-add-1"></i> 新增供应商
									</button>
								</div>
								<table class="layui-table" lay-skin="line" id="supplierPriceTable" style="background: white; border-radius: 2px; overflow: hidden;">
									<thead>
										<tr>
											<th>供应商名称</th>
											<th>供应商编号</th>
											<th>采购优先级</th>
											<th>采购下限</th>
											<th>采购上限</th>
											<th style="color: #ff4d4f; font-weight: 600;">含税单价 *</th>
											<th>税率(%)</th>
											<th style="color: #ff4d4f; font-weight: 600;">不含税单价 *</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="supplierPriceTableBody">
										<tr class="empty-row">
											<td colspan="9" style="text-align: center; color: #999; padding: 30px; background: #fafafa;">
												<i class="layui-icon layui-icon-template" style="font-size: 24px; color: #ccc; display: block; margin-bottom: 8px;"></i>
												<span style="font-size: 13px;">暂无供应商数据</span>
												<br><br>
												<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">
													<i class="layui-icon layui-icon-add-1"></i> 添加供应商
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						
						<!-- 外协单价 -->
						<div class="layui-tab-item">
							 
							
							<!-- 外协价格表格 -->
							<div style="background: #fafafa; padding: 16px; border-radius: 4px; margin-bottom: 16px; border: 1px solid #e8e8e8;">
								<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
									<h4 style="margin: 0; color: #262626; font-size: 14px; font-weight: 600;">
										外协价格管理
									</h4>
									<button type="button" class="layui-btn layui-btn-normal layui-btn-sm" onclick="addOutsourcePriceRow()">
										<i class="layui-icon layui-icon-add-1"></i> 新增外协商
									</button>
								</div>
								<table class="layui-table" lay-skin="line" id="outsourcePriceTable" style="background: white; border-radius: 2px; overflow: hidden;">
									<thead>
										<tr>
											<th>外协商名称</th>
											<th>外协商编号</th>
											<th>外协优先级</th>
											<th>外协下限</th>
											<th>外协上限</th>
											<th style="color: #ff4d4f; font-weight: 600;">含税单价 *</th>
											<th>税率(%)</th>
											<th style="color: #ff4d4f; font-weight: 600;">不含税单价 *</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="outsourcePriceTableBody">
										<tr class="empty-row">
											<td colspan="9" style="text-align: center; color: #999; padding: 30px; background: #fafafa;">
												<i class="layui-icon layui-icon-template" style="font-size: 24px; color: #ccc; display: block; margin-bottom: 8px;"></i>
												<span style="font-size: 13px;">暂无外协商数据</span>
												<br><br>
												<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">
													<i class="layui-icon layui-icon-add-1"></i> 添加外协商
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>

			<!-- 操作按钮区域 -->
			<div class="material-card">
				<div class="section-content" style="text-align: center; padding: 24px; border-top: 1px solid #e8e8e8; background: #fafafa;">
					<button class="layui-btn layui-btn-normal" lay-submit lay-filter="addSubmit" style="padding: 8px 24px; margin-right: 8px;">
						<i class="layui-icon layui-icon-ok"></i> 保存
					</button>
					<button type="reset" class="layui-btn layui-btn-primary" style="margin-right: 8px; padding: 8px 20px;">
						<i class="layui-icon layui-icon-refresh"></i> 重置
					</button>
					<button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()" style="padding: 8px 20px;">
						<i class="layui-icon layui-icon-close"></i> 取消
					</button>
				</div>
			</div>
		</form>
	</div>
</div>

{/block}
<!-- /主体 -->

{block name="style"}
<style>
.required::before {
	content: "*";
	color: #ff5722;
	margin-right: 4px;
}
.upload-area:hover {
	border-color: #009688;
}
.layui-form-label {
	width: 150px;
}
.layui-input-block {
	margin-left: 180px;
}
.quality-check-options {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	margin-top: 10px;
}
.check-option {
	display: flex;
	align-items: center;
}
.check-option .layui-form-checkbox {
	margin: 0;
}
.quality-exempt-section,
.quality-options-section {
	transition: all 0.3s ease;
}

/* 自动编号复选框样式 */
.auto-code-container {
	display: flex;
	align-items: center;
	background: #fff;
	border: 1px solid #e6e6e6;
	padding: 8px 12px;
	border-radius: 2px;
	white-space: nowrap;
	min-width: 120px;
}

.auto-code-container input[type="checkbox"] {
	margin-right: 5px;
}

.auto-code-container label {
	margin: 0;
	cursor: pointer;
	font-size: 12px;
	color: #666;
	user-select: none;
}

/* 禁用状态的输入框样式 */
input[name="material_code"]:disabled {
	background-color: #f5f5f5 !important;
	color: #999 !important;
	cursor: not-allowed !important;
}

/* 自动编号只读状态样式 */
input[name="material_code"].auto-code-readonly,
input[name="material_code"].auto-code-readonly:focus,
input[name="material_code"].auto-code-readonly:hover {
	background-color: #f5f5f5 !important;
	color: #999 !important;
	cursor: not-allowed !important;
	pointer-events: none !important;
	border-color: #e6e6e6 !important;
	box-shadow: none !important;
}

/* 确保Layui样式不会覆盖 */
.layui-input.auto-code-readonly,
.layui-input.auto-code-readonly:focus,
.layui-input.auto-code-readonly:hover {
	background-color: #f5f5f5 !important;
	color: #999 !important;
	cursor: not-allowed !important;
	pointer-events: none !important;
	border-color: #e6e6e6 !important;
	box-shadow: none !important;
}
</style>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','form','laydate','upload','element'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool, laydate = layui.laydate, upload = layui.upload, element = layui.element;

		// 基本单位变化监听
		form.on('select(base_unit)', function(data){
			var unitName = data.elem.options[data.elem.selectedIndex].text;
			// 更新所有显示基本单位的地方
			$('.layui-word-aux').each(function(){
				var text = $(this).text();
				if(text === '基本单位') {
					$(this).text(unitName || '基本单位');
				} else if(text.indexOf('含税 - 基本单位') !== -1) {
					$(this).text('含税 - ' + (unitName || '基本单位'));
				}
			});
		});

		
		// 文件上传
		upload.render({
			elem: '#uploadBtn',
			url: '/upload/file',
			accept: 'file',
			done: function(res){
				if(res.code == 0){
					layer.msg('上传成功');
				} else {
					layer.msg('上传失败：' + res.msg);
				}
			}
		});
		
		// 表单提交
		form.on('submit(addSubmit)', function(data){
			console.log('=== 表单提交数据 ===');
			console.log('data:', data);
			console.log('data.field:', data.field);
			console.log('data.field keys:', Object.keys(data.field));
			console.log('data.field length:', Object.keys(data.field).length);
			console.log('material_code:', data.field.material_code);
			console.log('auto_material_code:', data.field.auto_material_code);

			// 检查表单元素
			console.log('=== 表单元素检查 ===');
			var formElement = $('#addForm')[0];
			console.log('Form element:', formElement);
			console.log('Form elements count:', formElement.elements.length);

			// 手动收集表单数据
			var submitData = {};
			
			// 收集基础表单数据 - 确保收集所有标签页中的字段
			$('#addForm input, #addForm select, #addForm textarea').each(function() {
				var $this = $(this);
				var name = $this.attr('name');
				var value = $this.val();
				var type = $this.attr('type');

				if (name && !$this.prop('disabled')) {
					// 跳过动态表格中的字段，这些会单独处理
					if (name.indexOf('supplier_prices[') === 0 || name.indexOf('outsource_prices[') === 0) {
						return;
					}
					
					if (type === 'checkbox') {
						if (name.indexOf('[]') > -1) {
							// 处理数组类型的复选框
							if (!submitData[name]) submitData[name] = [];
							if ($this.is(':checked')) {
								submitData[name].push(value);
							}
						} else {
							// 处理单个复选框
							submitData[name] = $this.is(':checked') ? value : '';
						}
					} else if (type === 'radio') {
						if ($this.is(':checked')) {
							submitData[name] = value;
						}
					} else {
						// 确保所有字段都被收集，包括空值
						submitData[name] = value || '';
					}
				}
			});

			// 特别处理可能被遗漏的字段
			var specialFields = ['base_unit', 'default_warehouse', 'min_order_qty', 'min_package_qty', 'purchase_price_plan', 'price_match_logic'];
			specialFields.forEach(function(fieldName) {
				var field = $('input[name="' + fieldName + '"], select[name="' + fieldName + '"], textarea[name="' + fieldName + '"]');
				if (field.length > 0) {
					var fieldType = field.attr('type');
					if (fieldType === 'radio') {
						var checkedRadio = $('input[name="' + fieldName + '"]:checked');
						if (checkedRadio.length > 0) {
							submitData[fieldName] = checkedRadio.val();
						}
					} else {
						submitData[fieldName] = field.val() || '';
					}
				}
			});

			// 调试：输出收集到的字段
			console.log('=== 收集到的表单字段 ===');
			console.log('base_unit:', submitData.base_unit);
			console.log('default_warehouse:', submitData.default_warehouse);
			console.log('min_order_qty:', submitData.min_order_qty);
			console.log('min_package_qty:', submitData.min_package_qty);
			console.log('所有字段:', Object.keys(submitData));
			console.log('字段总数:', Object.keys(submitData).length);

			// 收集供应商价格数据
			var supplierPrices = [];
			$('#supplierPriceTableBody tr').each(function() {
				if ($(this).hasClass('empty-row')) return;
				
				var rowData = {};
				var hasData = false;
				
				$(this).find('input, select').each(function() {
					var name = $(this).attr('name');
					var value = $(this).val();
					
					if (name && value) {
						// 简单的字符串处理来提取字段名
						if (name.indexOf('supplier_prices[') === 0) {
							var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
							var end = name.lastIndexOf(']');
							if (start > 0 && end > start) {
								var fieldName = name.substring(start, end);
								rowData[fieldName] = value;
								hasData = true;
							}
						}
					}
				});
				
				if (hasData) {
					supplierPrices.push(rowData);
				}
			});
			
			if (supplierPrices.length > 0) {
				submitData['supplier_prices'] = supplierPrices;
			}

			// 收集外协价格数据
			var outsourcePrices = [];
			$('#outsourcePriceTableBody tr').each(function() {
				if ($(this).hasClass('empty-row')) return;
				
				var rowData = {};
				var hasData = false;
				
				$(this).find('input, select').each(function() {
					var name = $(this).attr('name');
					var value = $(this).val();
					
					if (name && value) {
						// 简单的字符串处理来提取字段名
						if (name.indexOf('outsource_prices[') === 0) {
							var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
							var end = name.lastIndexOf(']');
							if (start > 0 && end > start) {
								var fieldName = name.substring(start, end);
								rowData[fieldName] = value;
								hasData = true;
							}
						}
					}
				});
				
				if (hasData) {
					outsourcePrices.push(rowData);
				}
			});
			
			if (outsourcePrices.length > 0) {
				submitData['outsource_prices'] = outsourcePrices;
			}

			console.log('Final submit data:', submitData);
			console.log('Supplier prices:', submitData.supplier_prices);
			console.log('Outsource prices:', submitData.outsource_prices);

			let callback = function (e) {
				console.log('=== 服务器响应 ===');
				console.log('response:', e);
				layer.msg(e.msg);
				if (e.code == 0) {
					parent.layui.pageTable.reload();
					parent.layer.closeAll();
				}
			}

			tool.post("/material/archive/add", submitData, callback);
			return false;
		});
		
		// 备注字数统计
		$('textarea[name="remark"]').on('input', function() {
			var len = $(this).val().length;
			$(this).next('.layui-form-mid').text(len + ' / 100');
		});
		
		// 物料条形码字数统计
		$('input[name="material_shape"]').on('input', function() {
			var len = $(this).val().length;
			$(this).next('.layui-form-mid').text(len + ' / 44');
		});
		
		// 自动编号功能
		console.log('开始绑定自动编号事件');

		// 使用Layui的form.on监听复选框变化
		form.on('checkbox(auto_material_code)', function(data){
			console.log('=== 自动编号复选框状态改变 ===');
			var isChecked = data.elem.checked;
			var materialCodeInput = $('input[name="material_code"]');

			console.log('复选框状态:', isChecked);

			if(isChecked){
				console.log('启用自动编号');
				materialCodeInput.val('AUTO_GENERATE');
				materialCodeInput.prop('readonly', true);
				materialCodeInput.removeAttr('lay-verify');

				// 直接设置样式，确保生效
				materialCodeInput[0].style.setProperty('background-color', '#f5f5f5', 'important');
				materialCodeInput[0].style.setProperty('color', '#999', 'important');
				materialCodeInput[0].style.setProperty('cursor', 'not-allowed', 'important');
				materialCodeInput[0].style.setProperty('pointer-events', 'none', 'important');

				console.log('已设置只读样式');
			} else {
				console.log('关闭自动编号');
				materialCodeInput.val('');
				materialCodeInput.prop('readonly', false);
				materialCodeInput.attr('lay-verify', 'required');

				// 清除所有样式
				materialCodeInput[0].style.removeProperty('background-color');
				materialCodeInput[0].style.removeProperty('color');
				materialCodeInput[0].style.removeProperty('cursor');
				materialCodeInput[0].style.removeProperty('pointer-events');

				console.log('已恢复正常样式');
			}
		});



		// 质检信息交互

		// 质检管理开关
		form.on('switch(quality_management)', function(data){
			console.log('质检管理开关被触发:', data.elem.checked);
			var isChecked = data.elem.checked;
			if(isChecked){
				console.log('启用质检管理');
				// 启用质检管理时，显示免检设置，并默认选中所有质检选项
				$('.quality-exempt-section').show();
				$('.quality-options-section').show();
				// 默认选中所有质检选项
				$('input[name="quality_settings[]"]').prop('checked', true);
				// 重置免检设置为关闭状态
				$('input[name="quality_exempt"]').prop('checked', false);
				form.render();
			} else {
				console.log('关闭质检管理');
				// 关闭质检管理时，隐藏所有相关选项并重置状态
				$('.quality-exempt-section').hide();
				$('.quality-options-section').hide();
				$('input[name="quality_exempt"]').prop('checked', false);
				$('input[name="quality_settings[]"]').prop('checked', false);
				form.render();
			}
		});

		// 质检管理开关
		$('input[name="quality_management"]').on('change', function(){
			var isChecked = this.checked;
			if(isChecked){
				// 启用质检管理时，显示免检设置，并默认选中所有质检选项
				$('.quality-exempt-section').show();
				$('.quality-options-section').show();
				// 默认选中所有质检选项
				$('input[name="quality_settings[]"]').prop('checked', true);
				// 重置免检设置为关闭状态
				$('input[name="quality_exempt"]').prop('checked', false);
				form.render();
			} else {
				// 关闭质检管理时，隐藏所有相关选项并重置状态
				$('.quality-exempt-section').hide();
				$('.quality-options-section').hide();
				$('input[name="quality_exempt"]').prop('checked', false);
				$('input[name="quality_settings[]"]').prop('checked', false);
				form.render();
			}
		});
		
		// 免检设置开关
		$('input[name="quality_exempt"]').on('change', function(){
			var isChecked = this.checked;
			if(isChecked){
				// 开启免检设置时，取消所有质检选项
				$('input[name="quality_settings[]"]').prop('checked', false);
				form.render('checkbox');
			} else {
				// 关闭免检设置时，重新选中所有质检选项
				$('input[name="quality_settings[]"]').prop('checked', true);
				form.render('checkbox');
			}
		});

		// 确保表单元素已经渲染
		form.render();

		// 初始化自动编号状态
		setTimeout(function() {
			console.log('=== 初始化自动编号状态 ===');

			// 确保复选框初始状态为未选中
			$('#auto_material_code').prop('checked', false);

			// 确保输入框初始状态为可编辑
			var materialCodeInput = $('input[name="material_code"]');
			materialCodeInput.prop('readonly', false);
			materialCodeInput.attr('lay-verify', 'required');

			// 清除所有可能的样式
			materialCodeInput[0].style.removeProperty('background-color');
			materialCodeInput[0].style.removeProperty('color');
			materialCodeInput[0].style.removeProperty('cursor');
			materialCodeInput[0].style.removeProperty('pointer-events');

			// 重新渲染表单确保Layui状态同步
			form.render('checkbox');

			console.log('初始化完成');
		}, 100);

		// 初始化质检管理状态
		setTimeout(function() {
			var qualityManagementChecked = $('input[name="quality_management"]').is(':checked');
			if(qualityManagementChecked){
				$('.quality-exempt-section').show();
				$('.quality-options-section').show();
				// 如果质检管理已启用，检查免检设置状态
				var qualityExemptChecked = $('input[name="quality_exempt"]').is(':checked');
				if(!qualityExemptChecked){
					// 如果免检设置未开启，默认选中所有质检选项
					$('input[name="quality_settings[]"]').prop('checked', true);
					form.render('checkbox');
				}
			}
		}, 100);
		
		// 标签页切换事件
		element.on('tab(materialTabs)', function(data){
			console.log('切换到标签页：', data.index);
			// 可以在这里添加标签页切换时的逻辑
		});
		
		// 供应商数据 - 先定义空数组，然后通过模板语法填充
		var suppliersData = [];
		{if condition="$suppliers"}
		{volist name="suppliers" id="supplier"}
		suppliersData.push({
			id: '{$supplier.id}',
			code: '{$supplier.code}',
			name: '{$supplier.name}'
		});
		{/volist}
		{/if}

		// 调试信息：输出供应商数据
		console.log('Suppliers data loaded:', suppliersData);
		console.log('Suppliers count:', suppliersData.length);
		
		// 新增供应商价格行
		window.addSupplierPriceRow = function() {
			console.log('addSupplierPriceRow called');
			console.log('suppliersData:', suppliersData);

			var tbody = $('#supplierPriceTableBody');
			var emptyRow = tbody.find('.empty-row');

			// 如果存在空数据行，先移除
			if(emptyRow.length > 0) {
				emptyRow.remove();
			}

			var rowIndex = tbody.find('tr').length;
			var supplierOptions = '';

			// 构建供应商选项
			for(var i = 0; i < suppliersData.length; i++) {
				if(suppliersData[i].id) {
					supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" data-name="' + suppliersData[i].name + '">' + suppliersData[i].name + '</option>';
				}
			}

			console.log('supplierOptions generated:', supplierOptions);

			var newRow = '<tr>' +
				'<td>' +
					'<select name="supplier_prices[' + rowIndex + '][supplier_id]" class="layui-input supplier-select" onchange="updateSupplierInfo(this, ' + rowIndex + ')">' +
						'<option value="">请选择供应商</option>' +
						supplierOptions +
					'</select>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="supplier_prices[' + rowIndex + '][supplier_code]" placeholder="供应商编号" class="layui-input" readonly />' +
				'</td>' +
				'<td>' +
					'<select name="supplier_prices[' + rowIndex + '][priority]" class="layui-input">' +
						'<option value="">请选择</option>' +
						'<option value="首选">首选</option>' +
						'<option value="备选">备选</option>' +
					'</select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="supplier_prices[' + rowIndex + '][min_qty]" placeholder="下限" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="supplier_prices[' + rowIndex + '][max_qty]" placeholder="上限" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="supplier_prices[' + rowIndex + '][tax_price]" placeholder="含税单价" class="layui-input" step="0.01" required />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="supplier_prices[' + rowIndex + '][tax_rate]" placeholder="税率%" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="supplier_prices[' + rowIndex + '][no_tax_price]" placeholder="不含税单价" class="layui-input" step="0.01" required />' +
				'</td>' +
				'<td>' +
					'<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeSupplierPriceRow(this)">删除</button>' +
				'</td>' +
			'</tr>';
			
			tbody.append(newRow);
			console.log('New row added to table');

			// 重新渲染layui表单元素
			layui.use('form', function(){
				var form = layui.form;
				form.render('select'); // 重新渲染select
				console.log('Layui form re-rendered');

				// 检查新添加的select元素
				var newSelect = tbody.find('tr:last select');
				console.log('New select element:', newSelect);
				console.log('Select options count:', newSelect.find('option').length);
			});
		};
		
		// 更新供应商信息
		window.updateSupplierInfo = function(selectElement, rowIndex) {
			var selectedOption = $(selectElement).find('option:selected');
			var supplierCode = selectedOption.data('code');
			var supplierName = selectedOption.data('name');

			// 更新供应商编号
			$(selectElement).closest('tr').find('input[name="supplier_prices[' + rowIndex + '][supplier_code]"]').val(supplierCode || '');
		};

		// 更新外协商信息
		window.updateOutsourceInfo = function(selectElement, rowIndex) {
			var selectedOption = $(selectElement).find('option:selected');
			var supplierCode = selectedOption.data('code');
			var supplierName = selectedOption.data('name');

			// 更新外协商编号
			$(selectElement).closest('tr').find('input[name="outsource_prices[' + rowIndex + '][outsource_code]"]').val(supplierCode || '');
		};
		
		// 删除供应商价格行
		window.removeSupplierPriceRow = function(btn) {
			var tbody = $('#supplierPriceTableBody');
			$(btn).closest('tr').remove();
			
			// 如果没有数据行了，显示空数据提示
			if(tbody.find('tr').length === 0) {
				tbody.append('<tr class="empty-row">' +
					'<td colspan="9" style="text-align: center; color: #999; padding: 30px;">' +
						'暂无数据<br><br>' +
						'<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">新增一行</button>' +
					'</td>' +
				'</tr>');
			}
		};
		
		// 新增外协价格行
		window.addOutsourcePriceRow = function() {
			console.log('suppliersData:', suppliersData);

			var tbody = $('#outsourcePriceTableBody');
			var emptyRow = tbody.find('.empty-row');

			// 如果存在空数据行，先移除
			if(emptyRow.length > 0) {
				emptyRow.remove();
			}

			var rowIndex = tbody.find('tr').length;
			var supplierOptions = '';

			// 构建外协商（供应商）选项
			for(var i = 0; i < suppliersData.length; i++) {
				if(suppliersData[i].id) {
					supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" data-name="' + suppliersData[i].name + '">' + suppliersData[i].name + '</option>';
				}
			}

			console.log('outsource supplierOptions generated:', supplierOptions);

			var newRow = '<tr>' +
				'<td>' +
					'<select name="outsource_prices[' + rowIndex + '][supplier_id]" class="layui-input outsource-select" onchange="updateOutsourceInfo(this, ' + rowIndex + ')">' +
						'<option value="">请选择外协商</option>' +
						supplierOptions +
					'</select>' +
				'</td>' +
				'<td>' +
					'<input type="text" name="outsource_prices[' + rowIndex + '][outsource_code]" placeholder="外协商编号" class="layui-input" readonly />' +
				'</td>' +
				'<td>' +
					'<select name="outsource_prices[' + rowIndex + '][priority]" class="layui-input">' +
						'<option value="">请选择</option>' +
						'<option value="首选">首选</option>' +
						'<option value="备选">备选</option>' +
					'</select>' +
				'</td>' +
				'<td>' +
					'<input type="number" name="outsource_prices[' + rowIndex + '][min_qty]" placeholder="下限" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="outsource_prices[' + rowIndex + '][max_qty]" placeholder="上限" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="outsource_prices[' + rowIndex + '][tax_price]" placeholder="含税单价" class="layui-input" step="0.01" required />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="outsource_prices[' + rowIndex + '][tax_rate]" placeholder="税率%" class="layui-input" step="0.01" />' +
				'</td>' +
				'<td>' +
					'<input type="number" name="outsource_prices[' + rowIndex + '][no_tax_price]" placeholder="不含税单价" class="layui-input" step="0.01" required />' +
				'</td>' +
				'<td>' +
					'<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeOutsourcePriceRow(this)">删除</button>' +
				'</td>' +
			'</tr>';

			tbody.append(newRow);

			// 重新渲染表单元素
			layui.use('form', function(){
				var form = layui.form;
				form.render('select');
			});
		};
		
		// 删除外协价格行
		window.removeOutsourcePriceRow = function(btn) {
			var tbody = $('#outsourcePriceTableBody');
			$(btn).closest('tr').remove();

			// 如果没有数据行了，显示空数据提示
			if(tbody.find('tr').length === 0) {
				tbody.append('<tr class="empty-row">' +
					'<td colspan="9" style="text-align: center; color: #999; padding: 30px; background: #fafafa;">' +
						'<i class="layui-icon layui-icon-template" style="font-size: 24px; color: #ccc; display: block; margin-bottom: 8px;"></i>' +
						'<span style="font-size: 13px;">暂无外协商数据</span><br><br>' +
						'<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">' +
							'<i class="layui-icon layui-icon-add-1"></i> 添加外协商' +
						'</button>' +
					'</td>' +
				'</tr>');
			}
		};
	}
</script>
{/block}
<!-- /脚本 -->
