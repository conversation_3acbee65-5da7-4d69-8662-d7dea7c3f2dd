{extend name="../../base/view/common/base" /}
{block name="style"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header text-center">BOM管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline mr0">
                            <input type="text" name="keywords" placeholder="请输入产品名称或BOM编号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline mr0">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="0">草稿</option>
                                <option value="1">审核中</option>
                                <option value="2">已审核</option>
                                <option value="3">已拒绝</option>
                                <option value="4">已作废</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button class="layui-btn icon-btn" lay-filter="formReset" lay-submit>
                            <i class="layui-icon">&#xe669;</i>重置
                        </button>
                    </div>
                </div>
            </div>
            
            <table class="layui-table" id="dataTable" lay-filter="dataTable"></table>
        </div>
    </div>
</div>

<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe654;</i>新增</button>
    </div>
</script>

<!-- 操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    
    {{# if(d.status == 0 || d.status == 3){ }}
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="items">子项管理</a>
    <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="submit">提交审核</a>
    {{# } }}
    
    {{# if(d.status == 1 && d.can_cancel){ }}
    <!--<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="cancel">撤回审核</a>-->
    {{# } }}
    
    {{# if(d.status == 1 && d.can_check){ }}
    <!-- <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="check">审核</a> -->
    {{# } }}
    
    {{# if(d.status == 2){ }}
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="version">创建新版本</a>
    <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="invalid">作废</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="export">导出</a>
    {{# } }}
    
    {{# if(d.status == 0 || d.status == 3 || d.status == 4){ }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    {{# } }}
</script>

<!-- 状态格式化 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 0){ }}
    <span class="layui-badge layui-bg-orange">草稿</span>
    {{# }else if(d.status == 1){ }}
    <span class="layui-badge layui-bg-blue">审核中</span>
    {{# }else if(d.status == 2){ }}
    <span class="layui-badge layui-bg-green">已审核</span>
    {{# }else if(d.status == 3){ }}
    <span class="layui-badge layui-bg-red">已拒绝</span>
    {{# }else if(d.status == 4){ }}
    <span class="layui-badge">已作废</span>
    {{# } }}
</script>

<!-- 子项数量格式化 -->
<script type="text/html" id="itemCountTpl">
    <span class="layui-badge layui-bg-cyan">{{d.items_count}}</span>
</script>

<!-- 是否标准BOM格式化 -->
<script type="text/html" id="isStandardTpl">
    {{# if(d.is_standard == 1){ }}
    <span class="layui-badge layui-bg-green">是</span>
    {{# }else{ }}
    <span class="layui-badge layui-bg-gray">否</span>
    {{# } }}
</script>

{/block}

{block name="js"}
<script>
    const delay_num = 30;
	const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','admin'];
	function gouguInit() {
    layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        
        // 渲染表格
        var insTb = table.render({
            elem: '#dataTable',
            url: '{:url("engineering/bom/datalist")}',
            page: true,
            toolbar: '#toolbar',
            defaultToolbar: ['filter'],
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'bom_code', title: 'BOM编号', width: 160},
                {field: 'product_name', title: '产品名称', minWidth: 160},
                {field: 'version', title: '版本号', width: 100},
                {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
                {field: 'is_standard', title: '标准BOM', width: 100, templet: '#isStandardTpl'},
                {field: 'items_count', title: '子项数量', width: 100, templet: '#itemCountTpl', sort: true},
                {field: 'create_time', title: '创建时间', width: 160, sort: true},
                {field: 'admin_name', title: '创建人', width: 100},
                {title: '操作', toolbar: '#tableBar', width: 350, fixed: 'right'}
            ]],
            done: function (res, curr, count) {
                // 判断当前用户是否有权限审核和撤回
                for (var i = 0; i < res.data.length; i++) {
                    // 简单判断，实际项目中应该根据用户角色和权限判断
                    res.data[i].can_check = true; // 假设所有用户都有审核权限
                    res.data[i].can_cancel = (res.data[i].admin_id == '{$Think.session.admin.id}'); // 只有创建人可以撤回
                }
                // 不需要重新渲染表格，否则会导致无限循环请求
            }
        });
        
        // 工具条点击事件
        table.on('toolbar(dataTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'add') { // 添加
                showEditModel();
            }
        });
        
        // 行工具条点击事件
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'edit') { // 修改
                showEditModel(data);
            } else if (layEvent === 'del') { // 删除
                doDel(data.id);
            } else if (layEvent === 'view') { // 查看
                console.log('准备查看BOM，ID:', data.id);
                layer.open({
                    type: 2,
                    title: '查看BOM',
                    shadeClose: true,
                    shade: 0.3,
                    area: ['90%', '90%'],
                    content: '{:url("engineering/bom/view")}?id=' + data.id,
                    success: function(layero, index) {
                        console.log("查看BOM窗口打开成功");
                    }
                });
            } else if (layEvent === 'items') { // 子项管理
                console.log('准备管理子项，BOM ID:', data.id);
                layer.open({
                    type: 2,
                    title: '子项管理',
                    shadeClose: true,
                    shade: 0.3,
                    area: ['90%', '90%'],
                    content: '{:url("engineering/bom/items")}?bom_id=' + data.id,
                    success: function(layero, index) {
                        console.log("子项管理窗口打开成功");
                    }
                });
            } else if (layEvent === 'submit') { // 提交审核
                layer.open({
                    type: 2,
                    title: '提交审核',
                    content: '{:url("engineering/bom/submitCheck")}?id=' + data.id,
                    area: ['500px', '350px'],
                    maxmin: true
                });
            } else if (layEvent === 'check') { // 审核
                layer.open({
                    type: 2,
                    title: '审核BOM',
                    content: '{:url("engineering/bom/check")}?id=' + data.id,
                    area: ['90%', '90%'],
                    maxmin: true
                });
            } else if (layEvent === 'cancel') { // 撤回审核
                layer.open({
                    type: 2,
                    title: '撤回审核',
                    content: '{:url("engineering/bom/cancelCheck")}?id=' + data.id,
                    area: ['500px', '350px'],
                    maxmin: true
                });
            } else if (layEvent === 'invalid') { // 作废
                layer.open({
                    type: 2,
                    title: '作废BOM',
                    content: '{:url("engineering/bom/invalid")}?id=' + data.id,
                    area: ['500px', '350px'],
                    maxmin: true
                });
            } else if (layEvent === 'version') { // 创建新版本
                layer.open({
                    type: 2,
                    title: '创建新版本',
                    content: '{:url("engineering/bom/createVersion")}?id=' + data.id,
                    area: ['500px', '400px'],
                    maxmin: true
                });
            } else if (layEvent === 'export') { // 导出
                window.open('{:url("engineering/bom/export")}?id=' + data.id);
            }
        });
        
        // 通用弹窗方法 - 修改为直接使用layer.open
        function openDialog(options) {
            layer.open({
                type: 2,
                title: options.title,
                content: options.content,
                area: options.area,
                maxmin: true
            });
        }
        
        // 显示编辑弹窗
        function showEditModel(data) {
            layer.open({
                type: 2,
                title: (data ? '修改' : '添加') + 'BOM',
                content: '{:url("engineering/bom/add")}' + (data ? '?id=' + data.id : ''),
                area: ['80%', '90%'],
                maxmin: true
            });
        }
        
        // 删除
        function doDel(id) {
            layer.confirm('确定要删除此BOM吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (index) {
                layer.close(index);
                layer.load(2);
                $.ajax({
                    url: '{:url("engineering/bom/del")}',
                    type: 'delete',
                    data: {
                        id: id
                    },
                    dataType: 'json',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 0) {
                            layer.msg(res.msg, {icon: 1});
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg('请求错误', {icon: 2});
                    }
                });
            });
        }
        
        // 搜索
        form.on('submit(formSearch)', function (data) {
            insTb.reload({where: data.field}, 'data');
        });
        
        // 重置
        form.on('submit(formReset)', function (data) {
            $('input[name="keywords"]').val('');
            $('select[name="status"]').val('');
            form.render('select');
            insTb.reload({where: {}}, 'data');
        });
    });
    }
</script>
{/block} 