<?php

namespace app\material\model;

use think\Model;

class BomItem extends Model
{
    protected $table = 'oa_bom_item';
    
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'bom_id' => 'int',
        'material_code' => 'string',
        'material_name' => 'string',
        'material_category' => 'string',
        'specifications' => 'string',
        'model' => 'string',
        'unit' => 'string',
        'quantity' => 'float',
        'loss_rate' => 'float',
        'material_source' => 'string',
        'reference_bom' => 'string',
        'create_time' => 'int',
        'update_time' => 'int',
        'delete_time' => 'int'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = false;

    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;

    /**
     * 关联BOM主表
     */
    public function bom()
    {
        return $this->belongsTo(Bom::class, 'bom_id', 'id');
    }

    /**
     * 关联物料表
     */
    public function material()
    {
        return $this->belongsTo(Material::class, 'material_code', 'material_code');
    }

    /**
     * 获取物料来源文本
     */
    public function getMaterialSourceTextAttr($value, $data)
    {
        $sources = [
            '自购' => '自购',
            '自制' => '自制',
            '委外' => '委外'
        ];
        return $sources[$data['material_source']] ?? $data['material_source'];
    }

    /**
     * 创建时间格式化
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['create_time']);
    }

    /**
     * 更新时间格式化
     */
    public function getUpdateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['update_time']);
    }
}