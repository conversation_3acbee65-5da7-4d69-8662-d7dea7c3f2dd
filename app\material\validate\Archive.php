<?php
declare (strict_types = 1);

namespace app\material\validate;

use think\Validate;

class Archive extends Validate
{
    protected $rule = [
        'material_code'    => 'max:50|unique:material_archive',
        'material_name'    => 'require|max:100',
        'category_id'      => 'require|integer|gt:0',
        'material_source'  => 'max:50',
        'model'           => 'max:50',
        'category'        => 'max:50',
        'color'           => 'max:50',
        'material_level'  => 'max:50',
        'remark'          => 'max:500',
        'base_unit'       => 'integer|egt:0',
        'reference_cost'  => 'float|egt:0',
        'sales_price'     => 'float|egt:0',
        'min_sales_price' => 'float|egt:0',
        'max_sales_price' => 'float|egt:0',
        'min_order_qty'   => 'float|egt:0',
        'min_package_qty' => 'float|egt:0',
    ];

    protected $message = [
        'material_code.require'    => '物料编号不能为空',
        'material_code.max'        => '物料编号不能超过50个字符',
        'material_code.unique'     => '物料编号已存在',
        'material_name.require'    => '物料名称不能为空',
        'material_name.max'        => '物料名称不能超过100个字符',
        'category_id.require'      => '物料分类不能为空',
        'category_id.integer'      => '物料分类必须为整数',
        'category_id.gt'           => '请选择有效的物料分类',
        'base_unit.integer'        => '基本单位必须为整数',
        'base_unit.egt'            => '基本单位不能为负数',
        'reference_cost.float'     => '参考成本必须为数字',
        'reference_cost.egt'       => '参考成本不能为负数',
        'sales_price.float'        => '销售单价必须为数字',
        'sales_price.egt'          => '销售单价不能为负数',
        'min_sales_price.float'    => '最低销售单价必须为数字',
        'min_sales_price.egt'      => '最低销售单价不能为负数',
        'max_sales_price.float'    => '最高销售单价必须为数字',
        'max_sales_price.egt'      => '最高销售单价不能为负数',
        'min_order_qty.float'      => '最小起订量必须为数字',
        'min_order_qty.egt'        => '最小起订量不能为负数',
        'min_package_qty.float'    => '最小包装量必须为数字',
        'min_package_qty.egt'      => '最小包装量不能为负数',
        'remark.max'               => '备注不能超过500个字符',
    ];

    protected $scene = [
        'add'  => ['material_code', 'material_name', 'category_id', 'material_source', 'model', 'category', 'color', 'material_level', 'remark', 'reference_cost', 'sales_price', 'min_sales_price', 'max_sales_price', 'min_order_qty', 'min_package_qty'],
        'edit' => ['material_name', 'category_id', 'material_source', 'model', 'category', 'color', 'material_level', 'remark', 'reference_cost', 'sales_price', 'min_sales_price', 'max_sales_price', 'min_order_qty', 'min_package_qty'],
    ];
}