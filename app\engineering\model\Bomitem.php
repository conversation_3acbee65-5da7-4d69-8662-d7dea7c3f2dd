<?php
// +----------------------------------------------------------------------
// | 工艺管理模型
// +----------------------------------------------------------------------
namespace app\engineering\model;

use think\Model;
use think\facade\Db;

class Bomitem extends Model
{
    // 设置表名
    protected $name = 'bom_item';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联产品表
     */
    public function product()
    {
        return $this->belongsTo('app\engineering\model\Product', 'product_id', 'id');
    }
    
    /**
     * 获取指定BOM ID的所有子项
     * @param int $bom_id BOM ID
     * @return \think\Collection 子项集合
     */
    public function getItemsByBomId($bom_id)
    {
        // 启用调试模式，记录SQL查询
        \think\facade\Db::setConfig(['debug' => true]);
        
        // 使用Db类直接查询，不依赖模型的表名处理
        $items = \think\facade\Db::table($this->getTable())
            ->alias('bi')
            ->join(config('database.connections.mysql.prefix') . 'product p', 'p.id = bi.material_id', 'left')
            ->where('bi.bom_id', $bom_id)
            ->field('bi.*, p.title as material_name, p.material_code, p.specs as product_specs, p.unit as product_unit')
            ->order('bi.id asc')
            ->select();
        
        // 记录SQL查询
        \think\facade\Log::record('BOM子项查询SQL: ' . \think\facade\Db::getLastSql(), 'info');
        
        // 调试：打印product表中的一条记录以检查specs字段
        $product = \think\facade\Db::table(config('database.connections.mysql.prefix') . 'product')
            ->where('id', function($query) use ($bom_id) {
                $query->table($this->getTable())
                    ->where('bom_id', $bom_id)
                    ->limit(1)
                    ->field('material_id');
            })
            ->find();
        
        \think\facade\Log::record('产品信息示例: ' . json_encode($product, JSON_UNESCAPED_UNICODE), 'info');
        
        return $items;
    }
    
    /**
     * 批量添加BOM子项
     * @param array $items 子项数据数组
     * @return array 结果信息
     */
    public function batchAdd($items)
    {
        if (empty($items)) {
            return to_assign(1, '没有要添加的数据');
        }
        
        // 开启事务
        Db::startTrans();
        
        try {
            $success_count = 0;
            $fail_count = 0;
            $duplicate_count = 0;
            $bom_id = $items[0]['bom_id'] ?? 0;
            
            // 获取已存在的物料ID列表，用于去重
            $existing_material_ids = [];
            if (!empty($bom_id)) {
                $existing_material_ids = Db::name('bom_item')
                    ->where('bom_id', $bom_id)
                    ->column('material_id');
            }
            
            foreach ($items as $item) {
                // 检查必填项
                if (empty($item['material_id']) || empty($item['qty']) || $item['qty'] <= 0) {
                    $fail_count++;
                    continue;
                }
                
                // 检查是否重复
                if (in_array($item['material_id'], $existing_material_ids)) {
                    $duplicate_count++;
                    continue;
                }
                
                // 从物料获取名称等信息（如果不存在）
                if (empty($item['material_name']) || empty($item['material_code'])) {
                    $material = Db::name('product')
                        ->where('id', $item['material_id'])
                        ->find();
                    
                    if ($material) {
                        $item['material_name'] = $material['title'] ?? '';
                        $item['material_code'] = $material['material_code'] ?? '';
                        $item['uom_id'] = $material['uom_id'] ?? 0;
                        $item['uom_name'] = $material['unit'] ?? '';
                        $item['source_type'] = $material['source_type'] ?? 1;
                    }
                }
                
                // 插入数据
                $result = Db::name('bom_item')->insert($item);
                if ($result) {
                    $success_count++;
                    $existing_material_ids[] = $item['material_id'];
                } else {
                    $fail_count++;
                }
            }
            
            // 更新BOM的修改时间
            if ($success_count > 0 && !empty($bom_id)) {
                Db::name('bom_master')
                    ->where('id', $bom_id)
                    ->update(['update_time' => time()]);
            }
            
            // 提交事务
            Db::commit();
            
            // 返回结果
            if ($success_count > 0) {
                return to_assign(0, "导入成功！成功添加 {$success_count} 项，失败 {$fail_count} 项，重复 {$duplicate_count} 项。");
            } else {
                return to_assign(1, "导入失败！成功添加 {$success_count} 项，失败 {$fail_count} 项，重复 {$duplicate_count} 项。");
            }
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return to_assign(1, '导入失败，原因：' . $e->getMessage());
        }
    }
    
    /**
     * 更新子项排序
     * @param array $items 排序数据数组，格式：[['id' => 1, 'sort' => 10], ...]
     * @return array 结果信息
     */
    public function updateItemSort($items)
    {
        if (empty($items)) {
            return to_assign(1, '没有要更新的数据');
        }
        
        // 开启事务
        Db::startTrans();
        
        try {
            // 记录更新的BOM ID，用于后续更新修改时间
            $bom_ids = [];
            
            foreach ($items as $item) {
                if (empty($item['id'])) {
                    continue;
                }
                
                // 获取子项信息，主要是为了获取BOM ID
                $bom_item = $this->where('id', $item['id'])->find();
                if ($bom_item && !in_array($bom_item['bom_id'], $bom_ids)) {
                    $bom_ids[] = $bom_item['bom_id'];
                }
                
                // 更新排序
                $this->where('id', $item['id'])->update(['sort' => $item['sort'] ?? 0]);
            }
            
            // 更新BOM的修改时间
            foreach ($bom_ids as $bom_id) {
                Db::name('bom_master')
                    ->where('id', $bom_id)
                    ->update(['update_time' => time()]);
            }
            
            // 提交事务
            Db::commit();
            
            return to_assign(0, '更新排序成功');
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return to_assign(1, '更新排序失败，原因：' . $e->getMessage());
        }
    }
}