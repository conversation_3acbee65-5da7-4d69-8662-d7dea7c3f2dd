{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
		<div class="body-table">
			<div class="layui-row">
				<div class="layui-col-md3">
					<div class="category-tree" style="padding: 0; border-right: 1px solid #e6e6e6; height: 100%; background: #fafafa;">
						<div class="tree-header" style="padding: 15px 15px 10px 15px; border-bottom: 1px solid #e6e6e6; background: #f8f8f8;">
							<div style="display: flex; justify-content: space-between; align-items: center;">
								<span style="font-weight: bold; color: #333; font-size: 14px;">分类名称</span>
								<button class="layui-btn layui-btn-xs layui-btn-danger" id="addCategoryBtn" style="height: 24px; line-height: 24px;">新增分类</button>
							</div>
						</div>
						<div class="tree-content" style="padding: 10px 0;">
							<ul id="categoryTree" class="category-list">
								<li data-id="" class="category-item active">
									<span class="category-name">物料</span>
								</li>
								{volist name="categoryTree" id="category"}
								<li data-id="{$category.id}" class="category-item {$category.has_children ? 'has-children' : ''}" data-level="0">
									<div class="category-content">
										{if condition="$category.has_children"}
										<i class="expand-icon layui-icon layui-icon-right"></i>
										{else /}
										<span class="expand-placeholder"></span>
										{/if}
										<span class="category-name">{$category.name}</span>
										<div class="category-actions">
											<button class="action-btn add-sub-btn" title="添加下级分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-add-circle"></i>
											</button>
											<button class="action-btn edit-btn" title="编辑分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-edit"></i>
											</button>
											<button class="action-btn delete-btn" title="删除分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-close"></i>
											</button>
										</div>
									</div>
									{if condition="isset($category.children) && !empty($category.children)"}
									<ul class="sub-category-list" style="display: none;">
										{volist name="category.children" id="subCategory"}
										<li data-id="{$subCategory.id}" class="category-item sub-category" data-level="1">
											<div class="category-content">
												<span class="expand-placeholder"></span>
												<span class="category-name">{$subCategory.name}</span>
												<div class="category-actions">
													<button class="action-btn add-sub-btn" title="添加下级分类" data-id="{$subCategory.id}">
														<i class="layui-icon layui-icon-add-circle"></i>
													</button>
													<button class="action-btn edit-btn" title="编辑分类" data-id="{$subCategory.id}">
														<i class="layui-icon layui-icon-edit"></i>
													</button>
													<button class="action-btn delete-btn" title="删除分类" data-id="{$subCategory.id}">
														<i class="layui-icon layui-icon-close"></i>
													</button>
												</div>
											</div>
										</li>
										{/volist}
									</ul>
									{/if}
								</li>
								{/volist}
							</ul>
						</div>
					</div>
				</div>
				<div class="layui-col-md9">
					<form class="layui-form gg-form-bar border-x" id="barsearchform" style="border-left: none;">
						<div class="layui-input-inline" style="width:150px;">
							<input type="text" name="keywords" placeholder="输入关键字" class="layui-input" autocomplete="off" />
						</div>
						<div class="layui-input-inline" style="width:150px">
							<input type="hidden" name="category_id" value="" />
							<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
							<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
						</div>
					</form>
					<table class="layui-hide" id="table_archive" lay-filter="table_archive"></table>
				</div>
			</div>
		</div> 
	</div>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	 
	<button class="layui-btn layui-btn-sm" lay-event="addMaterial">
		<span>新增物料</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="import">
		<span>导入</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="export">
		<span>导出</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="delete">
		<span>删除</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batchModify">
		<span>批量修改</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="printBarcode">
		<span>批量打印二维码</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="printLabel">
		<span>批量条码标签</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="update">
		<span>更新</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="importCategory">
		<span>导入分类</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="importSubCategory">
		<span>导入子分类</span>
	</button>
  </div>
</script>

{/block}
<!-- /主体 -->

{block name="style"}
<style>
.category-tree {
	height: calc(100vh - 200px);
	overflow-y: auto;
}
.tree-header {
	position: sticky;
	top: 0;
	z-index: 10;
}
.tree-content {
	max-height: calc(100vh - 280px);
	overflow-y: auto;
}
.category-list {
	list-style: none;
	margin: 0;
	padding: 0;
}
.category-item {
	cursor: pointer;
	transition: all 0.2s;
	border-bottom: 1px solid #f0f0f0;
	font-size: 14px;
	color: #333;
	line-height: 1.5;
	position: relative;
}
.category-content {
	padding: 10px 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.category-item:hover .category-content {
	background-color: #f5f5f5;
}
.category-item:hover .category-actions {
	opacity: 1;
	visibility: visible;
}
.category-item.active .category-content {
	background-color: #fff;
	color: #ff5722;
	border-left: 3px solid #ff5722;
	padding-left: 12px;
	font-weight: 500;
}
.sub-category .category-content {
	padding-left: 35px;
	background-color: #fafafa;
}
.sub-category:hover .category-content {
	background-color: #f0f0f0;
}
.sub-category.active .category-content {
	background-color: #fff5f5;
	color: #ff5722;
	border-left: 3px solid #ff5722;
	padding-left: 32px;
}
.category-actions {
	display: flex;
	gap: 2px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.2s;
}
.action-btn {
	width: 20px;
	height: 20px;
	border: 1px solid #d9d9d9;
	background: #fff;
	border-radius: 50%;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;
	color: #666;
}
.action-btn:hover {
	transform: scale(1.05);
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.add-sub-btn:hover {
	background-color: #52c41a;
	border-color: #52c41a;
	color: #fff;
}
.edit-btn:hover {
	background-color: #1890ff;
	border-color: #1890ff;
	color: #fff;
}
.delete-btn:hover {
	background-color: #ff4d4f;
	border-color: #ff4d4f;
	color: #fff;
}
.action-btn i {
	font-size: 10px;
}
.category-item.expandable {
	position: relative;
}
.expand-icon {
	margin-right: 8px;
	font-size: 12px;
	color: #999;
	transition: transform 0.2s;
	cursor: pointer;
	width: 16px;
	text-align: center;
}
.expand-placeholder {
	width: 24px;
	display: inline-block;
}
.category-item.expanded .expand-icon {
	transform: rotate(90deg);
}
.sub-category-list {
	list-style: none;
	margin: 0;
	padding: 0;
}
.has-children > .category-content .expand-icon:hover {
	color: #333;
}
.category-name {
	flex: 1;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 10px;
}
.p-page .layui-col-md3 {
	padding-right: 0;
}
.p-page .layui-col-md9 {
	padding-left: 0;
}
</style>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus','form'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool, form = layui.form;
		
		// 展开/收起分类
		$(document).on('click', '.expand-icon', function(e) {
			e.stopPropagation();
			var $item = $(this).closest('.category-item');
			var $subList = $item.find('> .sub-category-list');
			
			if ($item.hasClass('expanded')) {
				$item.removeClass('expanded');
				$subList.slideUp(200);
			} else {
				$item.addClass('expanded');
				$subList.slideDown(200);
			}
		});
		
		// 分类点击事件
		$(document).on('click', '.category-item', function(e) {
			// 如果点击的是操作按钮或展开图标，不触发分类选择
			if ($(e.target).closest('.category-actions').length > 0 || $(e.target).hasClass('expand-icon')) {
				return;
			}
			
			$('.category-item').removeClass('active');
			$(this).addClass('active');
			var categoryId = $(this).data('id');
			$('[name="category_id"]').val(categoryId);
			layui.pageTable.reload({where:{category_id: categoryId}, page:{curr:1}});
		});
		
		// 添加下级分类
		$(document).on('click', '.add-sub-btn', function(e) {
			e.stopPropagation();
			var parentId = $(this).data('id');
			var parentName = $(this).closest('.category-item').find('.category-name').text();
			
			// 打开添加下级分类弹窗
			layer.open({
				type: 1,
				title: '新增分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="addSubCategoryForm">
							<input type="hidden" name="pid" value="${parentId}" />
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">父级分类</label>
								<div class="layui-input-block">
									<input type="text" value="${parentName}" class="layui-input" readonly style="background: #f5f5f5;" />
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveSubCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '350px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定添加下级分类表单提交事件
					form.on('submit(saveSubCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", data.field, callback);
						return false;
					});
				}
			});
		});
		
		// 编辑分类
		$(document).on('click', '.edit-btn', function(e) {
			e.stopPropagation();
			var categoryId = $(this).data('id');
			var categoryName = $(this).closest('.category-item').find('.category-name').text();
			
			// 打开编辑分类弹窗
			layer.open({
				type: 1,
				title: '编辑分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="editCategoryForm">
							<input type="hidden" name="id" value="${categoryId}" />
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" value="${categoryName}" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveEditCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '300px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定编辑表单提交事件
					form.on('submit(saveEditCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", data.field, callback);
						return false;
					});
				}
			});
		});
		
		// 删除分类
		$(document).on('click', '.delete-btn', function(e) {
			e.stopPropagation();
			var categoryId = $(this).data('id');
			var categoryName = $(this).closest('.category-item').find('.category-name').text();
			
			layer.confirm('确定要删除分类"' + categoryName + '"吗？', { 
				icon: 3, 
				title: '删除确认' 
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						// 重新加载页面
						location.reload();
					}
				}
				tool.post("/material/category/delete", { id: categoryId }, callback);
				layer.close(index);
			});
		});
		
		// 新增分类弹窗处理
		$(document).on('click', '#addCategoryBtn', function(e) {
			e.stopPropagation();
			
			// 打开新增分类弹窗
			layer.open({
				type: 1,
				title: '新增分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="newCategoryForm">
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveNewCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '300px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定新增分类表单提交事件
					form.on('submit(saveNewCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", {name: data.field.category_name, status: 1}, callback);
						return false;
					});
				}
			});
		});
		
		layui.pageTable = table.render({
			elem: "#table_archive"
			,title: "物料档案列表"
			,toolbar: "#toolbarDemo"
			,url: "/material/archive/index"
			,page: true
			,limit: 20
			,cellMinWidth: 80
			,height: 'full-152'
			,cols: [[ //表头
				{type: 'checkbox', fixed: 'left'},
				{
					field: 'material_image',
					title: '物料图片',
					align: 'center',
					width: 80,
					templet: function(d) {
						return '<div class="material-image" style="width:40px;height:40px;background:#f5f5f5;border-radius:4px;display:flex;align-items:center;justify-content:center;"><i class="layui-icon layui-icon-picture" style="font-size:20px;color:#ccc;"></i></div>';
					}
				},{
					field: 'material_name',
					title: '物料名称',
					minWidth: 200
				},{
					field: 'material_code',
					title: '物料编码',
					align: 'center',
					width: 150,
					sort: true
				},{
					field: 'sales_price',
					title: '销售单价(含税)-基本单位',
					align: 'center',
					width: 180,
					templet: function(d) {
						return d.sales_price ? '¥' + parseFloat(d.sales_price).toFixed(2) : '-';
					}
				},{
					field: 'min_sales_price',
					title: '最低销售单价(含税)-基本单位',
					align: 'center',
					width: 200,
					templet: function(d) {
						return d.min_sales_price ? '¥' + parseFloat(d.min_sales_price).toFixed(2) : '-';
					}
				},{
					field: 'max_sales_price',
					title: '最高销售单价',
					align: 'center',
					width: 120,
					templet: function(d) {
						return d.max_sales_price ? '¥' + parseFloat(d.max_sales_price).toFixed(2) : '-';
					}
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 280,
					align: 'center',
					ignoreExport:true,
					templet: function (d) {
						var html = '<div class="layui-btn-group">';
						html += '<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">复制</span>';
						html += '<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						html += '<span class="layui-btn layui-btn-xs" lay-event="detail">详情</span>';
						html += '<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
						html += '<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="printBarcode">打印二维码</span>';
						html += '<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="printLabel">打印条码标签</span>';
						html += '<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="downloadLabel">下载条码号</span>';
						html += '</div>';
						return html;
					}						
				}
			]]
		});
		
		//表头工具栏事件
		table.on('toolbar(table_archive)', function(obj){
			if (obj.event === 'add'){
				// 新增分类
				return;
			}
			if (obj.event === 'addMaterial'){
				tool.side("/material/archive/add");
				return;
			}
			if (obj.event === 'import'){
				tool.side("/material/archive/import");
				return;
			}
			if (obj.event === 'export'){
				var checkStatus = table.checkStatus('table_archive');
				var data = checkStatus.data;
				if(data.length === 0){
					layer.msg('请选择要导出的数据');
					return;
				}
				// 导出逻辑
				return;
			}
			if (obj.event === 'delete'){
				var checkStatus = table.checkStatus('table_archive');
				var data = checkStatus.data;
				if(data.length === 0){
					layer.msg('请选择要删除的数据');
					return;
				}
				layer.confirm('确定要删除选中的物料档案吗?', { icon: 3, title: '提示' }, function (index) {
					var ids = [];
					layui.each(data, function(i, item) {
						ids.push(item.id);
					});
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/material/archive/batchDelete", { ids: ids.join(',') }, callback);
					layer.close(index);
				});
				return;
			}
		});	
			
		table.on('tool(table_archive)',function (obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				// 复制物料
				tool.side("/material/archive/add?copy_id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/material/archive/add?id="+data.id);
				return;
			}
			if (obj.event === 'detail') {
				tool.side("/material/archive/view?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除该物料档案吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.post("/material/archive/delete", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'printBarcode') {
				// 打印二维码
				layer.msg('打印二维码功能开发中...');
				return;
			}
			if (obj.event === 'printLabel') {
				// 打印条码标签
				layer.msg('打印条码标签功能开发中...');
				return;
			}
			if (obj.event === 'downloadLabel') {
				// 下载条码号
				layer.msg('下载条码号功能开发中...');
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->