<?php
declare (strict_types = 1);

namespace app\material\model;

use think\Model;

class Category extends Model
{
    protected $table = 'oa_material_category';
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'pid'         => 'int',
        'name'        => 'string',
        'sort'        => 'int',
        'status'      => 'tinyint',
        'create_time' => 'int',
        'update_time' => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // 获取器 - 格式化创建时间
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化更新时间
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 关联子分类
    public function children()
    {
        return $this->hasMany(Category::class, 'pid', 'id')->where('status', 1);
    }
    
    // 关联父分类
    public function parent()
    {
        return $this->belongsTo(Category::class, 'pid', 'id');
    }
}