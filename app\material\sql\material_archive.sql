-- 物料分类表
DROP TABLE IF EXISTS `oa_material_category`;
CREATE TABLE `oa_material_category` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级分类ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料分类表';

-- 插入默认分类数据
INSERT INTO `oa_material_category` (`pid`, `name`, `sort`, `status`, `create_time`, `update_time`) VALUES
(0, '物料测试01', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '毛坯', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '配件', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '成品', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '冷风', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '砂片', 6, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 物料档案表
DROP TABLE IF EXISTS `oa_material_archive`;
CREATE TABLE `oa_material_archive` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '物料ID',
  `material_code` varchar(50) NOT NULL DEFAULT '' COMMENT '物料编号',
  `material_name` varchar(100) NOT NULL DEFAULT '' COMMENT '物料名称',
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物料分类ID',
  `material_source` varchar(50) NOT NULL DEFAULT '' COMMENT '物料来源：自制、外购、委外',
  `material_attachment` varchar(255) NOT NULL DEFAULT '' COMMENT '物料附件',
  `model` varchar(50) NOT NULL DEFAULT '' COMMENT '型号',
  `category` varchar(50) NOT NULL DEFAULT '' COMMENT '类别',
  `color` varchar(50) NOT NULL DEFAULT '' COMMENT '颜色',
  `material_level` varchar(50) NOT NULL DEFAULT '' COMMENT '物料等级',
  `remark` text COMMENT '备注',
  `material_shape` varchar(100) NOT NULL DEFAULT '' COMMENT '物料条形码',
  `attachment` varchar(255) NOT NULL DEFAULT '' COMMENT '附件',
  `reference_cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '参考成本价',
  `sales_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售单价（含税）',
  `min_sales_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低销售单价（含税）',
  `max_sales_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高销售单价（含税）',
  `is_price_range_check` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用价格区间校验：1启用，0禁用',
  `base_unit` varchar(20) NOT NULL DEFAULT '' COMMENT '基本单位',
  `default_warehouse` varchar(50) NOT NULL DEFAULT '' COMMENT '默认仓库',
  `min_order_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小起订量',
  `min_package_qty` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小包装量',
  `material_validity` int(11) NOT NULL DEFAULT '0' COMMENT '物料有效期（时间戳）',
  `cost_calculation` varchar(50) NOT NULL DEFAULT '' COMMENT '存货计价方法',
  `quality_management` tinyint(1) NOT NULL DEFAULT '0' COMMENT '质检管理：1启用，0禁用',
  `quality_settings` text COMMENT '质检设置（JSON格式）',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_material_code` (`material_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_material_name` (`material_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料档案表';

-- 插入示例数据
INSERT INTO `oa_material_archive` (`material_code`, `material_name`, `category_id`, `material_source`, `model`, `reference_cost`, `base_unit`, `admin_id`, `create_time`) VALUES
('BQ-I-G', '明星IPS光片', 1, '外购', 'BQ-I-G', 56.00, '平米', 1, UNIX_TIMESTAMP()),
('SLP-I-AG2', '三利通IPS胶砂', 2, '外购', 'SLP-I-AG2', 45.00, '千克', 1, UNIX_TIMESTAMP()),
('WLFL202506', '测试-001', 3, '自制', 'WLFL202506', 32.00, '个', 1, UNIX_TIMESTAMP()),
('WLFL202507', '音乐线缆-1', 4, '外购', 'WLFL202507', 28.00, '米', 1, UNIX_TIMESTAMP()),
('WLFL202508', '原材料-45#', 5, '委外', 'WLFL202508', 65.00, '套', 1, UNIX_TIMESTAMP()),
('WLFL202509', '原材料-甘', 6, '外购', 'WLFL202509', 42.00, '千克', 1, UNIX_TIMESTAMP());
