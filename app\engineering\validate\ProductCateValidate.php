<?php

namespace app\engineering\validate;

use think\Validate;

class ProductCateValidate extends Validate
{
    protected $rule = [
        'title' => 'require',
        'code' => 'max:100|regex:/^[a-zA-Z0-9\-]+$/|unique:ProductCate,code^id',
        'id' => 'require',
    ];

    protected $message = [
        'title.require' => '分类名称不能为空',
        'code.max' => '分类编码最多不能超过100个字符',
        'code.regex' => '分类编码只能包含字母、数字和横线',
        'code.unique' => '分类编码已存在，请更换',
        'id.require' => '缺少更新条件',
    ];

    protected $scene = [
        'add' => ['title', 'code'],
        'edit' => ['id', 'title', 'code'],
    ];
} 