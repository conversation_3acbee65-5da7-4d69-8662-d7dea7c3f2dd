{extend name="../../base/view/common/base" /}
{block name="style"}
<form class="layui-form layui-form-pane" action="{:url('engineering/bom/editItem')}" method="post">
    <div class="layui-form-item">
        <div class="layui-card">
            <div class="layui-card-header">BOM子项信息</div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">物料</label>
                            <div class="layui-input-block">
                                <input type="text" value="{$item.material_name}" class="layui-input" readonly>
                                <input type="hidden" name="material_id" value="{$item.material_id}">
                                <input type="hidden" name="material_name" value="{$item.material_name}">
                                <input type="hidden" name="material_code" value="{$item.material_code}">
                            </div>
                        </div>
                    </div>
                  
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">用量</label>
                            <div class="layui-input-block">
                                <input type="text" name="qty" value="{$item.qty}" placeholder="请输入用量" class="layui-input" lay-verify="required|number">
                            </div>
                        </div>
                    </div>
                  
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">单位名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="uom_name" value="{$item.uom_name}" class="layui-input" readonly>
                                <input type="hidden" name="uom_id" value="{$item.uom_id}">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">损耗率(%)</label>
                            <div class="layui-input-block">
                                <input type="text" name="loss_rate" value="{$item.loss_rate}" placeholder="请输入损耗率，0-100" class="layui-input" lay-verify="number">
                            </div>
                        </div>
                    </div>
                 
                    <div class="layui-col-md12">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$item.remark}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-form-item text-center">
        <input type="hidden" name="id" value="{$item.id}">
        <input type="hidden" name="bom_id" value="{$item.bom_id}">
        <button class="layui-btn" lay-filter="formSubmit" lay-submit>保存</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="var index = parent.layer.getFrameIndex(window.name); parent.layer.close(index);">取消</button>
    </div>
</form>
{/block}

{block name="js"}
<script>
    const delay_num = 30;
    const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker'];
    function gouguInit() {
    layui.use(['form', 'layer'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        
        // 表单提交事件
        form.on('submit(formSubmit)', function (data) {
            var loading = layer.load(2);
            $.ajax({
                url: data.form.action,
                type: 'post',
                data: data.field,
                dataType: 'json',
                success: function (res) {
                    layer.close(loading);
                    if (res.code === 0) {
                        // 通知父页面刷新表格
                        if (window.parent && window.parent.layui.table) {
                            window.parent.layui.table.reload('itemTable');
                        }
                        // 调用父页面可能定义的刷新方法
                        if (typeof parent.refreshParent === 'function') {
                            parent.refreshParent();
                        }
                        
                        // 显示成功消息并自动关闭窗口
                        layer.msg(res.msg, {
                            icon: 1,
                            time: 1000 // 缩短显示时间
                        }, function() {
                            // 关闭当前弹窗
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('请求异常', {icon: 2});
                }
            });
            return false;
        });
    });
    }
</script>
{/block} 