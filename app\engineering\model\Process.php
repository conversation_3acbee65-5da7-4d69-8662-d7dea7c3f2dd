<?php
// +----------------------------------------------------------------------
// | 工艺管理模型
// +----------------------------------------------------------------------
namespace app\engineering\model;

use think\Model;

class Process extends Model
{
    // 设置表名
    protected $name = 'engineering_process';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    /**
     * 获取工艺列表
     */
    public function getProcessList($where = [], $page = 1, $limit = 10, $order = 'id desc')
    {
        $list = $this->where($where)
            ->order($order)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->each(function($item) {
                // 获取关联产品信息
                if (!empty($item['product_id'])) {
                    $product = \think\facade\Db::name('product')
                        ->field('id,name,code')
                        ->where('id', $item['product_id'])
                        ->find();
                    $item['product_name'] = $product ? $product['name'] : '';
                    $item['product_code'] = $product ? $product['code'] : '';
                }
                
                // 获取创建人信息
                if (!empty($item['user_id'])) {
                    $user = \think\facade\Db::name('admin')
                        ->field('id,name')
                        ->where('id', $item['user_id'])
                        ->find();
                    $item['user_name'] = $user ? $user['name'] : '';
                }
                
                // 格式化日期
                if (!empty($item['create_time'])) {
                    $item['create_time'] = date('Y-m-d H:i', $item['create_time']);
                }
                if (!empty($item['update_time'])) {
                    $item['update_time'] = date('Y-m-d H:i', $item['update_time']);
                }
                
                return $item;
            });
            
        return $list;
    }
    
    /**
     * 获取工艺详情
     */
    public function getProcessDetail($id)
    {
        $detail = $this->where('id', $id)->find();
        if (!$detail) {
            return [];
        }
        
        // 解析工序数据
        if (!empty($detail['steps'])) {
            $detail['steps'] = json_decode($detail['steps'], true);
        }
        
        // 获取关联产品信息
        if (!empty($detail['product_id'])) {
            $product = \think\facade\Db::name('product')
                ->where('id', $detail['product_id'])
                ->find();
            $detail['product'] = $product;
        }
        
        return $detail;
    }
    
    /**
     * 根据产品ID获取工艺列表
     */
    public function getProcessByProduct($product_id)
    {
        return $this->where('product_id', $product_id)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 检查工艺名称或编码是否存在
     */
    public function checkExists($name, $code, $id = 0)
    {
        $where = [];
        if ($id > 0) {
            $where[] = ['id', '<>', $id];
        }
        
        $where[] = ['name|code', '=', $name];
        
        return $this->where($where)->find();
    }
} 