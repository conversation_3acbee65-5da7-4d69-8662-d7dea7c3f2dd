<?php
namespace app\customer\model;

use think\Model;
use think\facade\Db;

class Order extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'customer_order';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

        // 软删除
       // use \think\model\concern\SoftDelete;
       // protected $deleteTime = 'delete_time';
       // protected $defaultSoftDelete = 0;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 关联订单明细
    public function orderDetails()
    {
        return $this->hasMany('OrderDetail', 'order_id', 'id');
    }
    
    // 关联客户
    public function customer()
    {
        return $this->belongsTo('Customer', 'customer_id', 'id')->field(['id', 'name']);
    }
    
    // 关联创建者用户
    public function adduser()
    {
        return $this->belongsTo('app\user\model\Admin', 'create_user_id', 'id')->bind(['create_user_name' => 'name']);
    }
    
    // 关联审核者用户
    public function checkuser()
    {
        return $this->belongsTo('app\user\model\Admin', 'check_user_id', 'id')->bind(['check_username' => 'username']);
    }

       // 关联支付记录反向
       public function payments()
       {
           return $this->hasMany(OrderPayment::class, 'order_id');
       }

    // 获取开票金额
    public function getInvoiceAmount()
    {
        $amount = Db::name('invoice_order')
            ->where('order_id', $this->id)
            ->where('delete_time', 0)
            ->sum('order_amount');
        return $amount ?: 0;
    }

    // 获取开票状态文本
    public function getInvoiceStatusTextAttr($value, $data)
    {
        $totalAmount = $this->total_amount;
        $invoiceAmount = $this->getInvoiceAmount();
        
        if ($invoiceAmount <= 0) {
            return '未开票';
        } elseif ($invoiceAmount >= $totalAmount) {
            return '已开票完成';
        } else {
            return '部分开票(' . number_format($invoiceAmount, 2) . '/' . number_format($totalAmount, 2) . ')';
        }
    }

    // 获取开票状态样式
    public function getInvoiceStatusClassAttr($value, $data)
    {
        $totalAmount = $this->total_amount;
        $invoiceAmount = $this->getInvoiceAmount();
        
        if ($invoiceAmount <= 0) {
            return 'layui-bg-gray';
        } elseif ($invoiceAmount >= $totalAmount) {
            return 'layui-bg-green';
        } else {
            return 'layui-bg-orange';
        }
    }
} 