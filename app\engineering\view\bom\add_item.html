{extend name="../../base/view/common/base" /}
{block name="style"}
<form class="layui-form layui-form-pane" action="{:url('engineering/bom/addItem')}" method="post">
    <div class="layui-form-item">
        <div class="layui-card">
            <div class="layui-card-header">BOM子项信息</div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">物料</label>
                            <div class="layui-input-block">
                                <select name="material_id" lay-filter="material_id" lay-verify="required" lay-search>
                                    <option value="">请选择物料</option>
                                    {foreach $material_list as $vo}
                                    <option value="{$vo.id}" {if isset($item) && $item.material_id==$vo.id}selected{/if} data-code="{$vo.material_code}" data-source="{$vo.source_type}" data-unit="{$vo.unit}">{$vo.title}（{$vo.material_code}）</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                  
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">用量</label>
                            <div class="layui-input-block">
                                <input type="text" name="qty" value="{$item.qty|default='1'}" placeholder="请输入用量" class="layui-input" lay-verify="required|number">
                            </div>
                        </div>
                    </div>
                  
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">单位名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="uom_name" value="{$item.uom_name|default=''}" placeholder="例如g" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">损耗率(%)</label>
                            <div class="layui-input-block">
                                <input type="text" name="loss_rate" value="{$item.loss_rate|default='0'}" placeholder="请输入损耗率，0-100" class="layui-input" lay-verify="number">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">排序号</label>
                            <div class="layui-input-block">
                                <input type="number" name="sort" value="{$item.sort|default='10'}" placeholder="请输入排序号" class="layui-input" lay-verify="number">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$item.remark|default=''}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-form-item text-center">
        <input type="hidden" name="id" value="{$item.id|default=''}">
        <input type="hidden" name="bom_id" value="{$bom.id}">
        <button class="layui-btn" lay-filter="formSubmit" lay-submit>保存</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}

{block name="js"}
<script>
      const delay_num = 30;
	const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker'];
	function gouguInit() {
    layui.use(['form', 'layer'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        
        // 表单提交事件
        form.on('submit(formSubmit)', function (data) {
            var loading = layer.load(2);
            $.ajax({
                url: data.form.action,
                type: 'post',
                data: data.field,
                dataType: 'json',
                success: function (res) {
                    layer.close(loading);
                    if (res.code === 0) {
                        // 通知父页面刷新表格
                        if (window.parent && window.parent.layui.table) {
                            window.parent.layui.table.reload('itemTable');
                        }
                        // 调用父页面可能定义的刷新方法
                        if (typeof parent.refreshParent === 'function') {
                            parent.refreshParent();
                        }
                        
                        // 显示成功消息并自动关闭窗口
                        layer.msg(res.msg, {
                            icon: 1,
                            time: 1000 // 缩短显示时间
                        }, function() {
                            // 关闭当前弹窗
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('请求异常', {icon: 2});
                }
            });
            return false;
        });
        
        // 物料选择事件
        form.on('select(material_id)', function (data) {
            var elem = data.elem;
            var option = elem.options[elem.selectedIndex];
            
            if (option && option.value) {
                $('input[name="material_name"]').val(option.text.split('（')[0]);
                $('input[name="material_code"]').val($(option).data('code'));
                
                // 设置单位
                var unit = $(option).data('unit');
                if (unit) {
                    $('input[name="uom_name"]').val(unit);
                }
                
                // 设置来源类型
                var sourceType = $(option).data('source') || 1;
                $('input[name="source_type"][value="'+sourceType+'"]').prop('checked', true);
                form.render('radio');
            } else {
                $('input[name="material_name"]').val('');
                $('input[name="material_code"]').val('');
                $('input[name="uom_name"]').val('');
            }
        });
        
        // 单位选择事件
        form.on('select(uom_id)', function (data) {
            var elem = data.elem;
            var option = elem.options[elem.selectedIndex];
            
            if (option && option.value) {
                $('input[name="uom_name"]').val(option.text);
            } else {
                $('input[name="uom_name"]').val('');
            }
        });
    });
    }
</script>
{/block}