<?php
namespace app\customer\model;

use think\Model;

class Delivery extends Model
{
    // 设置表名
    protected $name = 'customer_order_delivery';
    
    // 设置主键
    protected $pk = 'id';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_PENDING = 0;    // 待发货
    const STATUS_PROCESSING = 1; // 已出库（仓库接单）
    const STATUS_COMPLETED = 2;  // 已完成
    const STATUS_CANCELLED = 3;  // 已取消

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '待处理',
            1 => '处理中',
            2 => '已完成',
            3 => '已取消',
        ];
        return isset($status[$data['status']]) ? $status[$data['status']] : '未知';
    }
    
    /**
     * 获取状态样式类
     */
    public function getStatusClassAttr($value, $data)
    {
        $status = [
            self::STATUS_PENDING => 'layui-bg-orange',
            self::STATUS_PROCESSING => 'layui-bg-blue',
            self::STATUS_COMPLETED => 'layui-bg-green',
            self::STATUS_CANCELLED => 'layui-bg-gray',
        ];
        return isset($status[$data['status']]) ? $status[$data['status']] : 'layui-bg-gray';
    }
    
    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id');
    }
    
    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo('Customer', 'customer_id', 'id');
    }
    
    /**
     * 关联发货商品
     */
    public function items()
    {
        return $this->hasMany('DeliveryItem', 'delivery_id', 'id');
    }
    
    /**
     * 关联日志
     */
    public function logs()
    {
        return $this->hasMany('DeliveryLog', 'delivery_id', 'id');
    }
    
    /**
     * 关联明细
     */
    public function details()
    {
        return $this->hasMany('DeliveryDetail', 'delivery_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\\base\\model\\Admin', 'create_user_id', 'id');
    }
    
    /**
     * 关联处理人
     */
    public function handler()
    {
        return $this->belongsTo('app\\base\\model\\Admin', 'handler_id', 'id');
    }
    
    /**
     * 获取发货方式文本
     */
    public function getDeliveryTypeTextAttr($value, $data)
    {
        $types = [
            1 => '公司物流',
            2 => '外部物流',
            3 => '客户自提'
        ];
        return isset($types[$data['delivery_type']]) ? $types[$data['delivery_type']] : '未知';
    }
} 