-- BOM主表
CREATE TABLE IF NOT EXISTS `oa_bom` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编号',
  `bom_name` varchar(100) NOT NULL COMMENT 'BOM名称',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `product_code` varchar(50) DEFAULT '' COMMENT '产品编号',
  `customer_name` varchar(100) DEFAULT '' COMMENT '客户名称',
  `customer_code` varchar(50) DEFAULT '' COMMENT '客户编码',
  `specifications` varchar(200) DEFAULT '' COMMENT '规格',
  `attributes` varchar(200) DEFAULT '' COMMENT '属性',
  `product_category` varchar(50) DEFAULT '' COMMENT '产品分类',
  `unit_status` varchar(20) DEFAULT '' COMMENT '单据状态',
  `remark` text COMMENT '备注',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0草稿，1已审核，2已停用',
  `create_user` varchar(50) DEFAULT '' COMMENT '创建人',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `bom_code` (`bom_code`),
  KEY `product_name` (`product_name`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM主表';

-- BOM物料清单表
CREATE TABLE IF NOT EXISTS `oa_bom_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bom_id` int(11) NOT NULL COMMENT 'BOM主表ID',
  `material_code` varchar(50) NOT NULL COMMENT '物料编号',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `material_category` varchar(50) DEFAULT '' COMMENT '物料分类',
  `specifications` varchar(200) DEFAULT '' COMMENT '规格',
  `model` varchar(50) DEFAULT '' COMMENT '型号',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `quantity` decimal(10,4) DEFAULT 0.0000 COMMENT '数量',
  `loss_rate` decimal(5,2) DEFAULT 0.00 COMMENT '损耗率(%)',
  `material_source` varchar(20) DEFAULT '自购' COMMENT '物料来源：自购、自制、委外',
  `reference_bom` varchar(50) DEFAULT '' COMMENT '引用BOM',
  `create_time` int(11) DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `bom_id` (`bom_id`),
  KEY `material_code` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM物料清单表';