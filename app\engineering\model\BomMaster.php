<?php
declare (strict_types = 1);
namespace app\engineering\model;

use think\Model;
use think\facade\Db;

class BomMaster extends Model
{
    //关联产品
    public function product()
    {
        return $this->belongsTo('Product', 'product_id', 'id');
    }

    /**
     * 获取BOM列表
     * @param array $where 查询条件
     * @param array $param 分页参数
     * @return array
     */
    public function datalist($where = [], $param = [])
    {
        // 查询总数
        $count = $this->alias('b')
            ->join('product p', 'b.product_id = p.id', 'left')
            ->where($where)
            ->count();
        
        // 排序处理
        $order = 'b.id desc';
        if (!empty($param['order'])) {
            $order = $param['order'] . ' ' . $param['dir'];
        }
        
        // 分页处理
        $limit = (int)($param['limit'] ?? 10);
        $page = (int)($param['page'] ?? 1);
        $start = ($page - 1) * $limit;
        
        // 查询数据
        $list = $this->alias('b')
            ->join('product p', 'b.product_id = p.id', 'left')
            ->field('b.*, p.title as product_title, p.material_code as product_material_code, '.
                    'p.source_type as product_source_type')
            ->where($where)
            ->limit($start, $limit)
            ->order($order)
            ->select()
            ->map(function($item) {
                // 格式化状态
                $statusMap = [
                    0 => '草稿',
                    1 => '审核中',
                    2 => '已审核',
                    3 => '已拒绝',
                    4 => '已作废'
                ];
                $item['status_text'] = $statusMap[$item['status']] ?? '未知';
                
                // 格式化时间
                if (!empty($item['create_time'])) {
                    $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                }
                if (!empty($item['update_time'])) {
                    $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
                }
                
                // 获取子项数量
                $item['items_count'] = Db::name('BomItem')
                    ->where('bom_id', $item['id'])
                    ->count();
                    
                return $item;
            });
        
        return [
            'count' => $count,
            'data' => $list,
        ];
    }
    
    /**
     * 添加BOM信息
     * @param array $param BOM数据
     * @return array
     */
    public function add($param)
    {
        try {
            // 预处理一些字段，确保安全
            if (isset($param['bom_code'])) {
                // 确保BOM编号只包含安全字符
                $oldCode = $param['bom_code'];
                $param['bom_code'] = preg_replace('/[^a-zA-Z0-9_]/', '', $param['bom_code']);
            }
            
            // 添加数据
            $id = $this->strict(false)->insertGetId($param);
            
            if ($id) {
                return to_assign(0, '添加成功', ['aid' => $id]);
            } else {
                return to_assign(1, '添加失败，数据库未返回ID');
            }
        } catch (\PDOException $e) {
            // 特殊处理数据库异常
            $errorCode = $e->getCode();
            $errorMessage = $e->getMessage();
            $errorInfo = $e->errorInfo ?? [];
            
            // 重复键错误
            if ($errorCode == 23000 || stripos($errorMessage, 'Duplicate entry') !== false) {
                if (stripos($errorMessage, 'idx_bom_code_version') !== false) {
                    return to_assign(1, '添加失败，该BOM编号和版本号组合已存在');
                }
                return to_assign(1, '添加失败，数据已存在：' . $errorMessage);
            }
            
            // 确保有错误信息
            $errorDesc = !empty($errorMessage) ? $errorMessage : '数据库错误';
            if (empty($errorDesc)) {
                $errorDesc = '数据库错误代码: ' . $errorCode;
            }
            
            // 其他数据库错误
            return to_assign(1, '添加失败，数据库错误：' . $errorDesc);
        } catch (\Throwable $t) {
            // 捕获所有可能的错误
            $errorType = get_class($t);
            $errorMessage = $t->getMessage();
            $errorFile = $t->getFile();
            $errorLine = $t->getLine();
            
            // 构建友好的错误信息
            $errorDesc = !empty($errorMessage) ? $errorMessage : '未知系统错误';
            
            return to_assign(1, '添加失败，系统错误：' . $errorDesc);
        }
    }
    
    /**
     * 编辑BOM信息
     * @param array $param BOM数据
     * @return array
     */
    public function edit($param)
    {
        try {
            // 更新数据
            $id = $param['id'];
            $res = $this->where('id', $id)->strict(false)->update($param);
            if ($res !== false) {
                return to_assign(0, '修改成功');
            } else {
                return to_assign(1, '修改失败');
            }
        } catch (\Exception $e) {
            return to_assign(1, '修改失败，原因：'.$e->getMessage());
        }
    }
    
    /**
     * 根据ID获取BOM信息
     * @param int $id BOM ID
     * @return array
     */
    public function getById($id)
    {
        $info = $this->where('id', $id)->find();
        if ($info) {
            // 状态文本转换
            $statusMap = [
                0 => '草稿',
                1 => '审核中',
                2 => '已审核',
                3 => '已拒绝',
                4 => '已作废'
            ];
            $info['status_text'] = $statusMap[$info['status']] ?? '未知';
            
            // 格式化时间
            if (!empty($info['create_time'])) {
                $info['create_time_text'] = $info['create_time'];
            }
            if (!empty($info['update_time'])) {
                $info['update_time_text'] = $info['update_time'];
            }
            
            // 获取管理员信息
            if (!empty($info['admin_id'])) {
                $admin = get_admin($info['admin_id']);
                $info['admin_name'] = $admin['name'] ?? '';
            }
            
            // 获取审核员信息
            if (!empty($info['check_admin_id'])) {
                $admin = get_admin($info['check_admin_id']);
                $info['check_admin_name'] = $admin['name'] ?? '';
            }
            
            return $info;
        } else {
            return [];
        }
    }
    
    /**
     * 软删除BOM信息
     * @param int $id BOM ID
     * @return array
     */
    public function delById($id)
    {
        try {
            // 检查BOM状态
            $info = $this->where('id', $id)->find();
            if (!$info) {
                return to_assign(1, 'BOM不存在');
            }
            
            if ($info['status'] == 1 || $info['status'] == 2) {
                return to_assign(1, '审核中或已审核的BOM不能删除');
            }
            
            // 开启事务
            Db::startTrans();
            
            // 删除BOM
            $this->where('id', $id)->update([
                'delete_time' => time(),
            ]);
            
            // 删除BOM子项
            Db::name('BomItem')->where('bom_id', $id)->update([
                'delete_time' => time(),
            ]);
            
            // 提交事务
            Db::commit();
            
            return to_assign(0, '删除成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return to_assign(1, '删除失败，原因：'.$e->getMessage());
        }
    }
    
    /**
     * 更新BOM状态
     * @param int $id BOM ID
     * @param int $status 状态值
     * @param int $admin_id 操作人ID
     * @return bool
     */
    public function updateStatus($id, $status, $admin_id = 0)
    {
        $data = [
            'status' => $status,
            'update_time' => time(),
        ];
        
        if ($status == 2 && $admin_id > 0) {
            // 审核通过时记录审核人
            $data['check_admin_id'] = $admin_id;
        }
        
        return $this->where('id', $id)->update($data) !== false;
    }
    
    /**
     * 创建新版本BOM
     * @param int $id 源BOM ID
     * @param string $version 新版本号
     * @param string $reason 变更原因
     * @param int $admin_id 操作人ID
     * @return array
     */
    public function createNewVersion($id, $version, $reason, $admin_id)
    {
        // 获取原BOM信息
        $bom = $this->where('id', $id)->find();
        if (!$bom) {
            return to_assign(1, '源BOM不存在');
        }
        
        // 检查版本号是否已存在
        $exists = $this->where('bom_code', $bom['bom_code'])
            ->where('version', $version)
            ->where('delete_time', 0)
            ->find();
            
        if ($exists) {
            return to_assign(1, '该版本号已存在，请使用其他版本号');
        }
        
        try {
            // 开启事务
            Db::startTrans();
            
            // 创建新版本BOM
            $newBom = $bom->toArray();
            unset($newBom['id']);
            $newBom['version'] = $version;
            $newBom['status'] = 0; // 草稿状态
            $newBom['is_standard'] = 0;
            $newBom['create_time'] = time();
            $newBom['update_time'] = time();
            $newBom['admin_id'] = $admin_id;
            $newBom['check_admin_id'] = 0;
            
            $newId = $this->strict(false)->insertGetId($newBom);
            
            // 复制BOM子项
            $items = Db::name('BomItem')
                ->where('bom_id', $id)
                ->where('delete_time', 0)
                ->select()
                ->toArray();
                
            foreach ($items as $item) {
                unset($item['id']);
                $item['bom_id'] = $newId;
                $item['create_time'] = time();
                Db::name('BomItem')->strict(false)->insert($item);
            }
            
            // 添加变更日志
            $admin = get_admin($admin_id);
            $logData = [
                'bom_id' => $newId,
                'bom_code' => $bom['bom_code'],
                'version_from' => $bom['version'],
                'version_to' => $version,
                'change_type' => 2, // 版本升级
                'change_reason' => $reason,
                'change_content' => '从'.$bom['version'].'升级到'.$version,
                'admin_id' => $admin_id,
                'admin_name' => $admin['name'] ?? '',
                'create_time' => time()
            ];
            Db::name('BomChangeLog')->strict(false)->insert($logData);
            
            // 提交事务
            Db::commit();
            
            return to_assign(0, '创建新版本成功', ['aid' => $newId]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return to_assign(1, '创建新版本失败，原因：'.$e->getMessage());
        }
    }
} 