{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.form-container {
    padding: 20px;
    background: #fff;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1E9FFF;
}
.info-item {
    margin-bottom: 10px;
}
.info-label {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    color: #666;
}
.info-value {
    color: #333;
}
.material-table {
    margin-top: 20px;
}
.material-table .layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.btn-group {
    margin-top: 20px;
    text-align: center;
}
.bom-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    color: #fff;
}
.status-draft { background-color: #999; }
.status-approved { background-color: #5FB878; }
.status-disabled { background-color: #FF5722; }
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="form-container">
    <!-- 基础资料 -->
    <div class="section-title">基础资料</div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">BOM编号：</span>
                <span class="info-value">{$bom.bom_code}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">BOM名称：</span>
                <span class="info-value">{$bom.bom_name}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">产品名称：</span>
                <span class="info-value">{$bom.product_name}</span>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">产品编号：</span>
                <span class="info-value">{$bom.product_code}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">客户名称：</span>
                <span class="info-value">{$bom.customer_name}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">客户编码：</span>
                <span class="info-value">{$bom.customer_code}</span>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">规格：</span>
                <span class="info-value">{$bom.specifications}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">属性：</span>
                <span class="info-value">{$bom.attributes}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">产品分类：</span>
                <span class="info-value">{$bom.product_category}</span>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">单据状态：</span>
                <span class="info-value">{$bom.unit_status}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">审核状态：</span>
                {if $bom.status == 0}
                    <span class="bom-status status-draft">草稿</span>
                {elseif $bom.status == 1}
                    <span class="bom-status status-approved">已审核</span>
                {elseif $bom.status == 2}
                    <span class="bom-status status-disabled">已停用</span>
                {/if}
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">创建人：</span>
                <span class="info-value">{$bom.create_user}</span>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{$bom.create_time_text}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">更新人：</span>
                <span class="info-value">{$bom.update_user}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{$bom.update_time_text}</span>
            </div>
        </div>
    </div>
    
    {if $bom.remark}
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="info-item">
                <span class="info-label">备注：</span>
                <span class="info-value">{$bom.remark}</span>
            </div>
        </div>
    </div>
    {/if}
    
    <!-- 物料列表 -->
    <div class="material-table">
        <div class="section-title">物料列表</div>
        
        <table class="layui-table">
            <thead>
                <tr>
                    <th width="80">BOM等级</th>
                    <th width="150">物料编号</th>
                    <th width="200">物料名称</th>
                    <th width="80">图片</th>
                    <th width="100">物料分类</th>
                    <th width="100">规格</th>
                    <th width="80">型号</th>
                    <th width="80">数量</th>
                    <th width="80">损耗率</th>
                    <th width="100">物料来源</th>
                    <th width="100">引用BOM</th>
                </tr>
            </thead>
            <tbody>
                {if isset($materials) && $materials}
                    {volist name="materials" id="material"}
                    <tr>
                        <td>一级</td>
                        <td>{$material.material_code}</td>
                        <td>{$material.material_name}</td>
                        <td><img src="/static/images/default.png" width="40" height="40"></td>
                        <td>{$material.material_category}</td>
                        <td>{$material.specifications}</td>
                        <td>{$material.model}</td>
                        <td>{$material.quantity}</td>
                        <td>{$material.loss_rate}%</td>
                        <td>{$material.material_source}</td>
                        <td>{$material.reference_bom}</td>
                    </tr>
                    {/volist}
                {else}
                <tr>
                    <td colspan="11" style="text-align:center;color:#999;">暂无物料数据</td>
                </tr>
                {/if}
            </tbody>
        </table>
    </div>
    
    <!-- 操作按钮 -->
    <div class="btn-group">
        <button type="button" class="layui-btn layui-btn-normal" onclick="editBom()">编辑</button>
        <button type="button" class="layui-btn layui-btn-warm" onclick="copyBom()">复制</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="exportBom()">导出</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="printBom()">打印</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
    </div>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var tool = layui.tool;
    
    // 编辑BOM
    window.editBom = function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        parent.layui.tool.side("/material/bom/edit?id={$bom.id}");
    };
    
    // 复制BOM
    window.copyBom = function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        parent.layui.tool.side("/material/bom/add?copy_id={$bom.id}");
    };
    
    // 导出BOM
    window.exportBom = function() {
        tool.post("/material/bom/export", {ids: ["{$bom.id}"]}, function(res){
            layer.msg(res.msg);
        });
    };
    
    // 打印BOM
    window.printBom = function() {
        window.print();
    };
}
</script>
{/block}
<!-- /脚本 -->