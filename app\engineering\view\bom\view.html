{extend name="../../base/view/common/base" /}
{block name="style"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">BOM详情</div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <table class="layui-table">
                        <colgroup>
                            <col width="150">
                            <col width="200">
                            <col width="150">
                            <col width="200">
                        </colgroup>
                        <tbody>
                            <tr>
                                <td style="text-align:right;">BOM编号:</td>
                                <td>{$detail.bom_code}</td>
                                <td style="text-align:right;">版本号:</td>
                                <td>{$detail.version}</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">产品名称:</td>
                                <td>{$detail.product_name}</td>
                                <td style="text-align:right;">产品编码:</td>
                                <td>{$detail.product_material_code|default=''}</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">状态:</td>
                                <td>{$detail.status_text}</td>
                                <td style="text-align:right;">标准BOM:</td>
                                <td>{if $detail.is_standard == 1}是{else}否{/if}</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">创建时间:</td>
                                <td>{$detail.create_time_text|default=''}</td>
                                <td style="text-align:right;">创建人:</td>
                                <td>{$detail.admin_name|default=''}</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">备注:</td>
                                <td colspan="3">{$detail.remarks|default=''}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">BOM子项</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-skin="line">
                                <thead>
                                    <tr>
                                        <th>物料编码</th>
                                        <th>物料名称</th>
                                        <th>规格</th>
                                        <th>用量</th>
                                        <th>单位</th>
                                        <th>损耗率(%)</th>
                                        <th>来源</th>
                                        <th>位置</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {volist name="items" id="item"}
                                    <tr>
                                        <td>{$item.material_code|default=''}</td>
                                        <td>{$item.material_name}</td>
                                        <td>{$item.product_specs|default=''}</td>
                                        <td>{$item.qty}</td>
                                        <td>{$item.uom_name|default=''}</td>
                                        <td>{$item.loss_rate|default='0'}</td>
                                        <td>
                                            {switch name="item.source_type"}
                                            {case value="1"}自制{/case}
                                            {case value="2"}外购{/case}
                                            {case value="3"}委外{/case}
                                            {default/}未知
                                            {/switch}
                                        </td>
                                        <td>{$item.position|default=''}</td>
                                        <td>{$item.remarks|default=''}</td>
                                    </tr>
                                    {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            {if condition="!empty($records)"}
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">审核记录</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-skin="line">
                                <thead>
                                    <tr>
                                        <th>操作类型</th>
                                        <th>操作人</th>
                                        <th>操作时间</th>
                                        <th>操作原因</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {volist name="records" id="record"}
                                    <tr>
                                        <td>
                                            {switch name="record.status"}
                                            {case value="0"}提交审核{/case}
                                            {case value="1"}审核通过{/case}
                                            {case value="2"}审核拒绝{/case}
                                            {case value="3"}撤回审核{/case}
                                            {case value="4"}作废{/case}
                                            {default/}未知操作
                                            {/switch}
                                        </td>
                                        <td>{$record.admin_name}</td>
                                        <td>{$record.check_time|date='Y-m-d H:i:s'}</td>
                                        <td>{$record.reason|default=''}</td>
                                    </tr>
                                    {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {/if}
            
            {if condition="!empty($logs)"}
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">变更记录</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-skin="line">
                                <thead>
                                    <tr>
                                        <th>变更类型</th>
                                        <th>变更内容</th>
                                        <th>变更原因</th>
                                        <th>操作人</th>
                                        <th>操作时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {volist name="logs" id="log"}
                                    <tr>
                                        <td>{$log.change_type}</td>
                                        <td>{$log.change_content}</td>
                                        <td>{$log.change_reason|default=''}</td>
                                        <td>{$log.admin_name}</td>
                                        <td>{$log.create_time|date='Y-m-d H:i:s'}</td>
                                    </tr>
                                    {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {/if}

             
        </div>
    </div>
    <div class="layui-tab layui-tab-card" style="margin:0; background-color:#fff;" lay-filter="purchase" id="purchaseTab">
        <ul class="layui-tab-title">
			<li class="layui-this" data-load="true">审批信息</li>
         
		</ul>
		<div class="layui-tab-content" style="padding:0;">
			<div class="layui-tab-item layui-show" style="padding-top:12px; background-color:#fff;">	
				<div id="checkBox" data-status="{$detail.check_status}" data-id="{$detail.id}" data-checkflowid="{$detail.check_flow_id}" class="px-3 pb-3"></div>
			</div>
           
		</div>
    </div>  
</div>
{/block}

{block name="js"}
<script>
    var order_id = "{$order.id|default=0}";
    var purchase_id = "{$order.id|default=0}";
    var auth = "{$auth|default=0}";
    var moduleInit = ['tool','oaCheck','oaPicker','oaEdit'];
    //var checking_btn = '<span class="layui-btn layui-btn-warm" data-event="stop" data-status="1">中止订单</span><span class="layui-btn layui-btn-danger" data-event="void" data-status="1">作废订单</span>';
    var checking_btn = '';
    
    function gouguInit() {
        var $ = layui.jquery,
            tool = layui.tool,
            layer = layui.layer,
            element = layui.element,
            oaCheck = layui.oaCheck,
            oaPicker = layui.oaPicker,
            oaEdit = layui.oaEdit;
        
        // 初始化审批信息组件
        try {
            oaCheck.init({
                check_name: 'bom_master',
                check_copy: 0,
                check_back: 0,
                checking_btn: checking_btn
            });
        } catch(e) {
            console.error('审批信息加载失败:', e);
            layer.msg('审批信息加载失败', {icon: 2});
        }
        
        // Tab切换事件
        element.on('tab(purchase)', function(data){
            let index = data.index;
            if(index == 1){
                loadPaymentLog();
            }else if(index === 2){ // 发货记录选项卡
                loadDeliveryLog();
        } else if(index === 3){ // 操作记录选项卡
            log();
        }
        });
        
        // 中止和作废订单操作
        $('body').on('click', '[data-event]', function() {
            let event = $(this).data('event');
            let status = $(this).data('status');
            let tips = '';
            
            if(event == 'stop') {
                tips = status == 1 ? '确定要中止此订单吗？' : '确定要取消中止此订单吗？';
            } else if(event == 'void') {
                tips = status == 1 ? '确定要作废此订单吗？' : '确定要取消作废此订单吗？';
            }
            
            if(tips) {
                layer.confirm(tips, {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    let url = `/purchase/order/${event}`;
                    let postData = {
                        id: order_id,
                        status: status
                    };
                    
                    if(status == 1) {
                        layer.prompt({
                            formType: 2,
                            title: '请输入' + (event == 'stop' ? '中止' : '作废') + '原因',
                            area: ['300px', '150px']
                        }, function(value, promptIndex) {
                            postData.remark = value;
                            submitAction(url, postData);
                            layer.close(promptIndex);
                        });
                    } else {
                        submitAction(url, postData);
                    }
                    
                    layer.close(index);
                });
            }
        });
        
        function submitAction(url, data) {
            tool.post(url, data, function(res) {
                if(res.code == 0) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        }
        
       
        
        // 提交审核按钮
        $('#btnSubmit').click(function() {
            layer.confirm('确定要提交此订单进行审核吗？', {icon: 3, title:'提示'}, function(index){
                tool.post('/purchase/order/submit', {id: order_id}, function(res) {
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
        
        // 删除按钮
        $('#btnDelete').click(function() {
            layer.confirm('确定要删除此订单吗？', {icon: 3, title:'提示'}, function(index){
                tool.post('/purchase/order/delete', {id: order_id}, function(res) {
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function() {
                            tool.back();
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
        
 
    
        
        // 取消按钮
        $('#btnCancel').click(function() {
            layer.confirm('确定要取消此订单吗？', {icon: 3, title:'提示'}, function(index){
                layer.prompt({
                    formType: 2,
                    title: '请输入取消原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    tool.post('/purchase/order/cancel', {id: order_id, remark: value}, function(res) {
                        if(res.code == 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(promptIndex);
                });
                layer.close(index);
            });
        });
        
        // 查看收货单
        $('.viewReceipt').click(function() {
            let receiptId = $(this).data('id');
            tool.side('/warehouse/Receipt/detail.html?id=' + receiptId);
        });
    }
</script>
{/block} 