{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.detail-panel {
    padding: 20px;
    margin-bottom: 20px;
}
.detail-title {
    font-weight: bold;
    margin-bottom: 15px;
}
.detail-item {
    margin-bottom: 10px;
}
.detail-label {
    font-weight: bold;
    display: inline-block;
    width: 100px;
}
</style>
{/block}

{block name="body"}
<div class="bg-white">
    <div class="bg-white detail-panel">
        <div class="detail-title">BOM基本信息</div>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="detail-item">
                    <span class="detail-label">BOM编号：</span>
                    <span>{$bom.bom_code ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">版本号：</span>
                    <span>{$bom.version ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">产品名称：</span>
                    <span>{$bom.product_name ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">状态：</span>
                    <span>{$bom.status_text ?? ''}</span>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="detail-item">
                    <span class="detail-label">创建时间：</span>
                    <span>{$bom.create_time_text ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">更新时间：</span>
                    <span>{$bom.update_time_text ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">创建人：</span>
                    <span>{$bom.admin_name ?? ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">备注：</span>
                    <span>{$bom.remark ?? ''}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-panel bg-white">
        <div class="detail-title">提交审核</div>
        <form class="layui-form" id="submitForm">
            <input type="hidden" name="id" value="{$bom.id ?? ''}">
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">审核说明</label>
                <div class="layui-input-block">
                    <textarea name="reason" placeholder="请输入审核说明" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitForm">提交审核</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name="script"}
<script>
     const delay_num = 30;
	const moduleInit = ['tool','tablePlus','laydatePlus','oaPicker','admin'];
	function gouguInit() {
layui.use(['form', 'table', 'layer'], function(){
    var form = layui.form;
    var table = layui.table;
    var layer = layui.layer;
    
    // 监听提交
    form.on('submit(submitForm)', function(data){
        var loading = layer.load(1, {shade: [0.1,'#fff']});
        $.ajax({
            url: '{:url("submitCheck")}',
            type: 'post',
            data: data.field,
            dataType: 'json',
            success: function(res){
                layer.close(loading);
                if(res.code === 0){
                    // 提交成功，显示成功提示，然后关闭弹窗并刷新父页面
                    parent.layer.msg(res.msg, {icon: 1, time: 1500}, function(){
                        // 关闭当前弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面
                        parent.location.reload();
                    });
                } else {
                    // 提交失败，显示错误信息
                    console.error('提交失败:', res);
                    var errorMsg = res.msg || '提交失败，请稍后重试';
                    layer.msg(errorMsg, {icon: 2, time: 3000});
                }
            },
            error: function(xhr, status, error){
                layer.close(loading);
                console.error('Ajax错误:', error, xhr.responseText);
                
                var errorMsg = '网络错误，请稍后重试';
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response && response.msg) {
                        errorMsg = response.msg;
                    }
                } catch(e) {
                    // 解析错误，使用默认错误消息
                }
                
                layer.msg(errorMsg, {icon: 2, time: 3000});
            }
        });
        return false;
    });
});
}

// 关闭弹窗
function closeDialog() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}
</script>
{/block} 